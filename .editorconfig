# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSON
[*.{js,jsx,ts,tsx,json,mjs}]
indent_size = 2

# CSS, SCSS, LESS
[*.{css,scss,less}]
indent_size = 2

# HTML
[*.{html,htm}]
indent_size = 2

# Markdown
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# YAML
[*.{yml,yaml}]
indent_size = 2

# XML
[*.xml]
indent_size = 2

# Shell scripts
[*.{sh,bash}]
indent_size = 4

# Python
[*.py]
indent_size = 4

# Makefile
[Makefile]
indent_style = tab

# Package files
[package.json]
indent_size = 2

# Environment files
[.env*]
trim_trailing_whitespace = false

# Git files
[.gitignore]
trim_trailing_whitespace = true

# Docker files
[Dockerfile*]
indent_size = 4

# SQL files
[*.sql]
indent_size = 2
