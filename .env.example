# =============================================================================
# 环境变量配置模板
# =============================================================================
# 复制此文件为 .env.local 并填入实际值
# 注意：不要在此文件中填入真实的密钥或敏感信息
# =============================================================================

# -----------------------------------------------------------------------------
# 应用基础配置
# -----------------------------------------------------------------------------
# 应用运行环境 (development | production | staging)
NODE_ENV=development

# 服务器端口
PORT=3001

# -----------------------------------------------------------------------------
# 前端配置 (Vite 环境变量，必须以 VITE_ 开头)
# -----------------------------------------------------------------------------
# API 基础 URL - 开发环境使用完整URL，生产环境使用相对路径
VITE_API_BASE_URL=http://localhost:3001/api
VITE_SERVER_URL=http://localhost:3001

# 应用环境标识
VITE_ENV=development

# -----------------------------------------------------------------------------
# 数据库配置
# -----------------------------------------------------------------------------
# PostgreSQL 数据库连接 (用于 Prisma 连接池)
POSTGRES_PRISMA_URL=postgresql://username:password@localhost:5432/database_name

# PostgreSQL 数据库直连 (用于 Prisma 迁移和直接连接)
POSTGRES_URL_NON_POOLING=postgresql://username:password@localhost:5432/database_name

# 通用数据库 URL (备用)
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# -----------------------------------------------------------------------------
# 文件存储配置
# -----------------------------------------------------------------------------
# Vercel Blob 存储令牌 (用于文件上传)
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token_here

# 本地文件上传目录 (用于本地部署)
UPLOAD_DIR=./uploads

# 文件上传限制 (字节)
MAX_FILE_SIZE=52428800

# -----------------------------------------------------------------------------
# 阿里云配置 (用于阿里云部署)
# -----------------------------------------------------------------------------
# 阿里云 OSS 访问密钥
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret

# 阿里云 OSS 配置
ALIYUN_OSS_REGION=oss-cn-hangzhou
ALIYUN_OSS_BUCKET=your_bucket_name
ALIYUN_OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com

# -----------------------------------------------------------------------------
# 安全配置
# -----------------------------------------------------------------------------
# JWT 密钥 (用于身份验证)
JWT_SECRET=your_super_secret_jwt_key_here

# 管理员账户配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_admin_password

# CORS 允许的源 (生产环境)
CORS_ORIGIN=https://yourdomain.com

# 会话密钥
SESSION_SECRET=your_session_secret_here

# -----------------------------------------------------------------------------
# 第三方服务配置
# -----------------------------------------------------------------------------
# Neon 数据库项目 ID (如果使用 Neon)
NEON_PROJECT_ID=your_neon_project_id

# Stack 项目配置 (如果使用)
STACK_PROJECT_ID=your_stack_project_id
STACK_PUBLISHABLE_CLIENT_KEY=your_stack_client_key
STACK_SECRET_SERVER_KEY=your_stack_server_key

# -----------------------------------------------------------------------------
# 开发工具配置
# -----------------------------------------------------------------------------
# 是否启用调试模式
DEBUG=false

# 日志级别 (error | warn | info | debug)
LOG_LEVEL=info

# 是否启用 API 文档
ENABLE_API_DOCS=true

# =============================================================================
# 生产环境配置示例
# =============================================================================
# 生产环境时，请使用以下配置：
#
# NODE_ENV=production
# PORT=3000
# VITE_API_BASE_URL=/api
# VITE_SERVER_URL=
# VITE_ENV=production
# DEBUG=false
# LOG_LEVEL=warn
# ENABLE_API_DOCS=false
# =============================================================================
