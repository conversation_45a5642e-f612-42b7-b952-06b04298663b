name: 🔄 持续集成

on:
  push:
    branches: [main, dev, staging]
  pull_request:
    branches: [main, dev]

env:
  NODE_VERSION: "18"
  CACHE_KEY_PREFIX: "node-cache"

jobs:
  # 代码质量检查
  lint-and-format:
    name: 🔍 代码质量检查
    runs-on: ubuntu-latest

    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 📦 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: 📦 安装依赖
        run: npm ci

      - name: 🔍 ESLint 检查
        run: npm run lint

      - name: 💅 Prettier 格式检查
        run: npm run format:check

      - name: 📝 TypeScript 类型检查
        run: npm run type-check

      - name: 🔍 检查未使用的导入
        run: npm run lint:unused

  # 环境配置验证
  env-validation:
    name: 🔧 环境配置验证
    runs-on: ubuntu-latest

    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 📦 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: 📦 安装依赖
        run: npm ci

      - name: 🔧 验证环境配置模板
        run: |
          # 复制示例配置进行验证
          cp .env.example .env.local
          # 设置测试用的环境变量
          echo "DATABASE_URL=postgresql://test:test@localhost:5432/test" >> .env.local
          echo "JWT_SECRET=test_jwt_secret_for_ci_validation_only_not_for_production_use" >> .env.local
          echo "ADMIN_USERNAME=admin" >> .env.local
          echo "ADMIN_PASSWORD=test_password" >> .env.local
          # 运行验证
          npm run env:validate

  # 构建测试
  build:
    name: 🏗️ 构建测试
    runs-on: ubuntu-latest
    needs: [lint-and-format, env-validation]

    strategy:
      matrix:
        node-version: [18, 20]

    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 📦 设置 Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - name: 📦 安装依赖
        run: npm ci

      - name: 🔧 设置测试环境变量
        run: |
          cp .env.example .env.local
          echo "DATABASE_URL=postgresql://test:test@localhost:5432/test" >> .env.local
          echo "JWT_SECRET=test_jwt_secret_for_ci_build_only_not_for_production_use" >> .env.local
          echo "ADMIN_USERNAME=admin" >> .env.local
          echo "ADMIN_PASSWORD=test_password" >> .env.local

      - name: 🏗️ 构建项目
        run: npm run build

      - name: 📦 上传构建产物
        if: matrix.node-version == 18
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: dist/
          retention-days: 7

  # 安全检查
  security:
    name: 🔒 安全检查
    runs-on: ubuntu-latest

    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 📦 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: 📦 安装依赖
        run: npm ci

      - name: 🔍 npm 安全审计
        run: npm audit --audit-level=moderate

      - name: 🔒 检查敏感信息
        run: |
          # 检查是否有敏感信息被意外提交
          if git log --all --full-history -- .env .env.local | grep -q "commit"; then
            echo "❌ 发现 .env 文件在 Git 历史中"
            exit 1
          fi

          # 检查当前文件中的敏感信息模式
          if grep -r "password.*=" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" src/; then
            echo "❌ 源代码中发现可能的硬编码密码"
            exit 1
          fi

          echo "✅ 安全检查通过"

  # 部署到 Vercel (仅在 main 分支)
  deploy-vercel:
    name: 🚀 部署到 Vercel
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 📦 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: 📦 安装依赖
        run: npm ci

      - name: 📦 下载构建产物
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: dist/

      - name: 🚀 部署到 Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: "--prod"

  # 通知
  notify:
    name: 📢 构建通知
    runs-on: ubuntu-latest
    needs: [lint-and-format, env-validation, build, security]
    if: always()

    steps:
      - name: 📢 构建成功通知
        if: ${{ needs.lint-and-format.result == 'success' && needs.env-validation.result == 'success' && needs.build.result == 'success' && needs.security.result == 'success' }}
        run: |
          echo "✅ 所有检查通过！"
          echo "🎉 项目构建成功"

      - name: 📢 构建失败通知
        if: ${{ needs.lint-and-format.result == 'failure' || needs.env-validation.result == 'failure' || needs.build.result == 'failure' || needs.security.result == 'failure' }}
        run: |
          echo "❌ 构建失败！"
          echo "请检查以下步骤的错误信息："
          echo "- 代码质量检查: ${{ needs.lint-and-format.result }}"
          echo "- 环境配置验证: ${{ needs.env-validation.result }}"
          echo "- 构建测试: ${{ needs.build.result }}"
          echo "- 安全检查: ${{ needs.security.result }}"
          exit 1
