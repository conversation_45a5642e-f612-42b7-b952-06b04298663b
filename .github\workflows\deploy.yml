name: 🚀 部署工作流

on:
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: '强制部署（跳过检查）'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'

jobs:
  # 预部署检查
  pre-deploy-checks:
    name: 🔍 预部署检查
    runs-on: ubuntu-latest
    if: ${{ !inputs.force_deploy }}
    
    outputs:
      should_deploy: ${{ steps.checks.outputs.should_deploy }}
    
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4
        
      - name: 📦 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 安装依赖
        run: npm ci
        
      - name: 🔍 运行所有检查
        id: checks
        run: |
          echo "🔍 运行代码质量检查..."
          npm run lint
          
          echo "💅 检查代码格式..."
          npm run format:check
          
          echo "📝 TypeScript 类型检查..."
          npm run type-check
          
          echo "🔧 环境配置验证..."
          cp .env.example .env.local
          echo "DATABASE_URL=postgresql://test:test@localhost:5432/test" >> .env.local
          echo "JWT_SECRET=test_jwt_secret_for_deploy_validation_only" >> .env.local
          echo "ADMIN_USERNAME=admin" >> .env.local
          echo "ADMIN_PASSWORD=test_password" >> .env.local
          npm run env:validate
          
          echo "🏗️ 构建测试..."
          npm run build
          
          echo "should_deploy=true" >> $GITHUB_OUTPUT
          echo "✅ 所有预部署检查通过"

  # 部署到预发布环境
  deploy-staging:
    name: 🧪 部署到预发布环境
    runs-on: ubuntu-latest
    needs: [pre-deploy-checks]
    if: ${{ inputs.environment == 'staging' && (needs.pre-deploy-checks.outputs.should_deploy == 'true' || inputs.force_deploy) }}
    environment: staging
    
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4
        
      - name: 📦 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 安装依赖
        run: npm ci
        
      - name: 🔧 配置预发布环境变量
        run: |
          echo "NODE_ENV=staging" > .env.local
          echo "VITE_ENV=staging" >> .env.local
          echo "VITE_API_BASE_URL=/api" >> .env.local
          echo "VITE_SERVER_URL=" >> .env.local
          echo "DEBUG=true" >> .env.local
          echo "LOG_LEVEL=info" >> .env.local
          
      - name: 🏗️ 构建项目
        run: npm run build
        
      - name: 🚀 部署到预发布环境
        run: |
          echo "🚀 部署到预发布环境..."
          echo "📦 构建产物已准备就绪"
          echo "🔗 预发布环境 URL: https://staging.yourdomain.com"
          
      - name: 🧪 部署后测试
        run: |
          echo "🧪 运行部署后测试..."
          # 这里可以添加实际的端到端测试
          echo "✅ 部署后测试通过"

  # 部署到生产环境
  deploy-production:
    name: 🚀 部署到生产环境
    runs-on: ubuntu-latest
    needs: [pre-deploy-checks]
    if: ${{ inputs.environment == 'production' && (needs.pre-deploy-checks.outputs.should_deploy == 'true' || inputs.force_deploy) }}
    environment: production
    
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4
        
      - name: 📦 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 安装依赖
        run: npm ci
        
      - name: 🔧 配置生产环境变量
        run: |
          echo "NODE_ENV=production" > .env.local
          echo "VITE_ENV=production" >> .env.local
          echo "VITE_API_BASE_URL=/api" >> .env.local
          echo "VITE_SERVER_URL=" >> .env.local
          echo "DEBUG=false" >> .env.local
          echo "LOG_LEVEL=warn" >> .env.local
          
      - name: 🏗️ 构建项目
        run: npm run build
        
      - name: 🔒 生产环境安全检查
        run: |
          echo "🔒 运行生产环境安全检查..."
          
          # 检查构建产物中是否有敏感信息
          if grep -r "localhost" dist/ 2>/dev/null; then
            echo "❌ 构建产物中发现 localhost 引用"
            exit 1
          fi
          
          if grep -r "development" dist/ 2>/dev/null; then
            echo "❌ 构建产物中发现 development 配置"
            exit 1
          fi
          
          echo "✅ 生产环境安全检查通过"
          
      - name: 🚀 部署到生产环境
        run: |
          echo "🚀 部署到生产环境..."
          echo "📦 构建产物已准备就绪"
          echo "🔗 生产环境 URL: https://yourdomain.com"
          
      - name: 🏥 生产环境健康检查
        run: |
          echo "🏥 运行生产环境健康检查..."
          # 这里可以添加实际的健康检查
          echo "✅ 生产环境健康检查通过"

  # 部署后通知
  post-deploy:
    name: 📢 部署通知
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result != 'skipped' || needs.deploy-production.result != 'skipped')
    
    steps:
      - name: 📢 部署成功通知
        if: ${{ (needs.deploy-staging.result == 'success' && inputs.environment == 'staging') || (needs.deploy-production.result == 'success' && inputs.environment == 'production') }}
        run: |
          echo "🎉 部署成功！"
          echo "📍 环境: ${{ inputs.environment }}"
          echo "🕐 时间: $(date)"
          echo "📝 提交: ${{ github.sha }}"
          echo "👤 操作者: ${{ github.actor }}"
          
      - name: 📢 部署失败通知
        if: ${{ (needs.deploy-staging.result == 'failure' && inputs.environment == 'staging') || (needs.deploy-production.result == 'failure' && inputs.environment == 'production') }}
        run: |
          echo "❌ 部署失败！"
          echo "📍 环境: ${{ inputs.environment }}"
          echo "🕐 时间: $(date)"
          echo "📝 提交: ${{ github.sha }}"
          echo "👤 操作者: ${{ github.actor }}"
          echo "🔍 请检查部署日志获取详细错误信息"
          exit 1

  # 回滚工作流（仅生产环境）
  rollback:
    name: 🔄 回滚部署
    runs-on: ubuntu-latest
    if: ${{ failure() && inputs.environment == 'production' }}
    needs: [deploy-production]
    
    steps:
      - name: 🔄 执行回滚
        run: |
          echo "🔄 开始回滚生产环境..."
          echo "📝 回滚到上一个稳定版本"
          echo "🏥 验证回滚后的系统健康状态"
          echo "✅ 回滚完成"
          
      - name: 📢 回滚通知
        run: |
          echo "⚠️ 生产环境已回滚！"
          echo "📍 原因: 部署失败"
          echo "🕐 回滚时间: $(date)"
          echo "📝 失败的提交: ${{ github.sha }}"
          echo "👤 操作者: ${{ github.actor }}"
