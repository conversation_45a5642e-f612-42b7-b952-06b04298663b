#!/usr/bin/env sh

# 验证提交信息格式
# 支持的格式：
# feat: 新功能
# fix: 修复bug
# docs: 文档更新
# style: 代码格式化
# refactor: 重构
# test: 测试相关
# chore: 构建过程或辅助工具的变动

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ 提交信息格式不正确！"
    echo ""
    echo "正确格式："
    echo "  feat: 添加新功能"
    echo "  fix: 修复bug"
    echo "  docs: 更新文档"
    echo "  style: 代码格式化"
    echo "  refactor: 重构代码"
    echo "  test: 添加测试"
    echo "  chore: 构建工具或依赖更新"
    echo ""
    echo "示例："
    echo "  feat: 添加用户登录功能"
    echo "  fix: 修复文件上传bug"
    echo "  docs: 更新API文档"
    echo ""
    exit 1
fi

echo "✅ 提交信息格式正确"
