#!/usr/bin/env sh

echo "🔍 执行推送前检查..."

# 检查是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    echo "❌ 存在未提交的更改，请先提交所有更改"
    exit 1
fi

# 运行类型检查
echo "📝 运行 TypeScript 类型检查..."
npm run type-check
if [ $? -ne 0 ]; then
    echo "❌ TypeScript 类型检查失败"
    exit 1
fi

# 运行测试（如果存在）
if [ -f "package.json" ] && grep -q '"test"' package.json; then
    echo "🧪 运行测试..."
    npm test
    if [ $? -ne 0 ]; then
        echo "❌ 测试失败"
        exit 1
    fi
fi

# 检查环境变量配置
echo "🔧 验证环境变量配置..."
npm run env:validate
if [ $? -ne 0 ]; then
    echo "❌ 环境变量配置验证失败"
    exit 1
fi

# 检查是否有敏感信息
echo "🔒 检查敏感信息..."
if git diff --cached --name-only | xargs grep -l "password\|secret\|key\|token" 2>/dev/null | grep -v ".example" | grep -v "docs/" | grep -v "README"; then
    echo "⚠️  警告：检测到可能包含敏感信息的文件"
    echo "请确认这些文件不包含真实的密码、密钥或令牌"
    read -p "确认继续推送？(y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "❌ 推送已取消"
        exit 1
    fi
fi

echo "✅ 所有检查通过，准备推送..."
