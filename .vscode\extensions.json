{
  "recommendations": [
    // 必需扩展
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    
    // React 开发
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    
    // Git 工具
    "eamodio.gitlens",
    "mhutchie.git-graph",
    
    // 开发工具
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-css-peek",
    "bradlc.vscode-tailwindcss",
    
    // 数据库
    "ms-vscode.vscode-postgres",
    "ckolkman.vscode-postgres",
    
    // 环境和配置
    "mikestead.dotenv",
    "editorconfig.editorconfig",
    
    // 代码质量
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    "gruntfuggly.todo-tree",
    
    // 文档和注释
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced",
    "aaron-bond.better-comments",
    
    // 主题和图标
    "pkief.material-icon-theme",
    "github.github-vscode-theme",
    
    // 实用工具
    "formulahendry.auto-close-tag",
    "ms-vscode.vscode-color-picker",
    "christian-kohler.npm-intellisense",
    "ms-vscode.vscode-npm-script",
    
    // 调试和测试
    "ms-vscode.vscode-node-debug2",
    "hbenl.vscode-test-explorer"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify",
    "ms-vscode.vscode-css-formatter"
  ]
}
