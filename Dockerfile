# 多阶段构建 Dockerfile
# 用于生产环境的优化构建

# ================================
# 基础镜像阶段
# ================================
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# 复制 package 文件
COPY package*.json ./

# ================================
# 依赖安装阶段
# ================================
FROM base AS deps

# 安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 安装所有依赖（包括开发依赖）
FROM base AS deps-dev
RUN npm ci

# ================================
# 构建阶段
# ================================
FROM deps-dev AS builder

# 复制源代码
COPY . .

# 设置构建环境变量
ENV NODE_ENV=production
ENV VITE_ENV=production
ENV VITE_API_BASE_URL=/api
ENV VITE_SERVER_URL=

# 生成 Prisma 客户端
RUN npx prisma generate

# 构建前端
RUN npm run build

# ================================
# 生产运行阶段
# ================================
FROM base AS runner

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 设置生产环境
ENV NODE_ENV=production
ENV PORT=3000

# 复制生产依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制构建产物
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/backend ./backend
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/package*.json ./

# 复制必要的配置文件
COPY --from=builder /app/.env.production ./.env.production

# 创建上传目录
RUN mkdir -p uploads && chown -R nextjs:nodejs uploads

# 切换到非 root 用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# 启动命令
CMD ["npm", "start"]
