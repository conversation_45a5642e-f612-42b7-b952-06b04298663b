# 开发环境 Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    curl \
    && rm -rf /var/cache/apk/*

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 设置开发环境变量
ENV NODE_ENV=development
ENV PORT=3001
ENV VITE_PORT=5173

# 暴露端口
EXPOSE 3001 5173

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# 启动开发服务器
CMD ["npm", "run", "dev"]
