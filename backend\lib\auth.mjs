/**
 * 身份验证和授权模块
 * 提供安全的用户认证功能
 */

import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { getValidatedEnv } from './env-validation.mjs';

const env = getValidatedEnv();

/**
 * 生成 JWT Token
 */
export function generateToken(payload, expiresIn = '24h') {
  return jwt.sign(payload, env.JWT_SECRET, { expiresIn });
}

/**
 * 验证 JWT Token
 */
export function verifyToken(token) {
  try {
    return jwt.verify(token, env.JWT_SECRET);
  } catch (error) {
    throw new Error('无效的访问令牌');
  }
}

/**
 * 哈希密码
 */
export async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

/**
 * 验证密码
 */
export async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

/**
 * 验证管理员凭据
 */
export async function validateAdminCredentials(username, password) {
  // 检查用户名
  if (username !== env.ADMIN_USERNAME) {
    return false;
  }
  
  // 在生产环境中，密码应该是哈希值
  // 这里为了兼容性，支持明文密码（仅开发环境）
  if (env.NODE_ENV === 'development' && password === env.ADMIN_PASSWORD) {
    return true;
  }
  
  // 生产环境应该使用哈希密码验证
  try {
    return await verifyPassword(password, env.ADMIN_PASSWORD);
  } catch (error) {
    // 如果不是哈希值，尝试直接比较（向后兼容）
    return password === env.ADMIN_PASSWORD;
  }
}

/**
 * 中间件：验证 JWT Token
 */
export function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
  
  if (!token) {
    return res.status(401).json({ error: '访问令牌缺失' });
  }
  
  try {
    const decoded = verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: '访问令牌无效' });
  }
}

/**
 * 中间件：验证管理员权限
 */
export function requireAdmin(req, res, next) {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ error: '需要管理员权限' });
  }
  next();
}

/**
 * 生成安全的随机密钥
 */
export function generateSecretKey(length = 64) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 密码强度检查
 */
export function checkPasswordStrength(password) {
  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    numbers: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  };
  
  const score = Object.values(checks).filter(Boolean).length;
  
  return {
    score,
    strength: score < 3 ? 'weak' : score < 4 ? 'medium' : 'strong',
    checks,
    suggestions: [
      !checks.length && '密码至少需要8个字符',
      !checks.uppercase && '添加大写字母',
      !checks.lowercase && '添加小写字母',
      !checks.numbers && '添加数字',
      !checks.special && '添加特殊字符'
    ].filter(Boolean)
  };
}

/**
 * 会话管理
 */
export class SessionManager {
  constructor() {
    this.sessions = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // 每分钟清理一次
  }
  
  createSession(userId, userData = {}) {
    const sessionId = this.generateSessionId();
    const session = {
      id: sessionId,
      userId,
      userData,
      createdAt: new Date(),
      lastAccess: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时
    };
    
    this.sessions.set(sessionId, session);
    return sessionId;
  }
  
  getSession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) return null;
    
    if (session.expiresAt < new Date()) {
      this.sessions.delete(sessionId);
      return null;
    }
    
    session.lastAccess = new Date();
    return session;
  }
  
  destroySession(sessionId) {
    return this.sessions.delete(sessionId);
  }
  
  cleanup() {
    const now = new Date();
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.expiresAt < now) {
        this.sessions.delete(sessionId);
      }
    }
  }
  
  generateSessionId() {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }
  
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.sessions.clear();
  }
}

// 全局会话管理器实例
export const sessionManager = new SessionManager();
