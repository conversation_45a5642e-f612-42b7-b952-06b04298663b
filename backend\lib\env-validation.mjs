/**
 * 环境变量验证和配置管理
 * 确保所有必需的环境变量都已正确设置
 */

import { config } from 'dotenv';
import { existsSync } from 'fs';
import { join } from 'path';

/**
 * 环境变量配置定义
 */
const ENV_CONFIG = {
  // 必需的环境变量
  required: {
    NODE_ENV: {
      type: 'string',
      values: ['development', 'staging', 'production'],
      description: '应用运行环境'
    },
    PORT: {
      type: 'number',
      min: 1000,
      max: 65535,
      description: '服务器端口'
    },
    DATABASE_URL: {
      type: 'string',
      pattern: /^postgresql:\/\/.+/,
      description: '数据库连接URL'
    }
  },
  
  // 可选的环境变量（有默认值）
  optional: {
    DEBUG: {
      type: 'boolean',
      default: false,
      description: '是否启用调试模式'
    },
    LOG_LEVEL: {
      type: 'string',
      values: ['error', 'warn', 'info', 'debug'],
      default: 'info',
      description: '日志级别'
    },
    MAX_FILE_SIZE: {
      type: 'number',
      default: 52428800, // 50MB
      description: '文件上传大小限制（字节）'
    },
    CORS_ORIGIN: {
      type: 'string',
      default: '*',
      description: 'CORS允许的源'
    },
    JWT_SECRET: {
      type: 'string',
      minLength: 32,
      description: 'JWT密钥'
    },
    ADMIN_USERNAME: {
      type: 'string',
      default: 'admin',
      description: '管理员用户名'
    },
    ADMIN_PASSWORD: {
      type: 'string',
      minLength: 8,
      description: '管理员密码'
    }
  },
  
  // 环境特定的必需变量
  environmentSpecific: {
    production: ['JWT_SECRET', 'ADMIN_PASSWORD'],
    staging: ['JWT_SECRET'],
    development: []
  }
};

/**
 * 验证环境变量类型和值
 */
function validateEnvVar(key, value, config) {
  if (!value && config.default !== undefined) {
    return config.default;
  }
  
  if (!value) {
    throw new Error(`环境变量 ${key} 是必需的: ${config.description}`);
  }
  
  // 类型验证
  switch (config.type) {
    case 'number':
      const num = Number(value);
      if (isNaN(num)) {
        throw new Error(`环境变量 ${key} 必须是数字`);
      }
      if (config.min !== undefined && num < config.min) {
        throw new Error(`环境变量 ${key} 不能小于 ${config.min}`);
      }
      if (config.max !== undefined && num > config.max) {
        throw new Error(`环境变量 ${key} 不能大于 ${config.max}`);
      }
      return num;
      
    case 'boolean':
      return value === 'true' || value === '1';
      
    case 'string':
      if (config.values && !config.values.includes(value)) {
        throw new Error(`环境变量 ${key} 必须是以下值之一: ${config.values.join(', ')}`);
      }
      if (config.pattern && !config.pattern.test(value)) {
        throw new Error(`环境变量 ${key} 格式不正确`);
      }
      if (config.minLength && value.length < config.minLength) {
        throw new Error(`环境变量 ${key} 长度不能少于 ${config.minLength} 个字符`);
      }
      return value;
      
    default:
      return value;
  }
}

/**
 * 加载环境配置文件
 */
function loadEnvFiles() {
  const envFiles = [
    '.env.local',           // 本地覆盖（最高优先级）
    `.env.${process.env.NODE_ENV}`, // 环境特定配置
    '.env'                  // 默认配置
  ];
  
  for (const file of envFiles) {
    if (existsSync(file)) {
      config({ path: file, override: false });
      console.log(`✓ 已加载环境配置文件: ${file}`);
    }
  }
}

/**
 * 验证环境变量
 */
export function validateEnvironment() {
  console.log('🔍 开始验证环境变量...');
  
  // 加载环境配置文件
  loadEnvFiles();
  
  const env = process.env.NODE_ENV || 'development';
  const validatedEnv = {};
  const warnings = [];
  
  try {
    // 验证必需的环境变量
    for (const [key, config] of Object.entries(ENV_CONFIG.required)) {
      validatedEnv[key] = validateEnvVar(key, process.env[key], config);
    }
    
    // 验证可选的环境变量
    for (const [key, config] of Object.entries(ENV_CONFIG.optional)) {
      validatedEnv[key] = validateEnvVar(key, process.env[key], config);
    }
    
    // 验证环境特定的必需变量
    const envSpecificRequired = ENV_CONFIG.environmentSpecific[env] || [];
    for (const key of envSpecificRequired) {
      if (!process.env[key]) {
        throw new Error(`环境变量 ${key} 在 ${env} 环境中是必需的`);
      }
    }
    
    // 安全检查
    if (env === 'production') {
      // 生产环境安全检查
      if (validatedEnv.ADMIN_PASSWORD === 'admin' || validatedEnv.ADMIN_PASSWORD.length < 12) {
        warnings.push('⚠️  生产环境建议使用更强的管理员密码（至少12位）');
      }
      
      if (validatedEnv.JWT_SECRET.includes('dev') || validatedEnv.JWT_SECRET.includes('test')) {
        throw new Error('生产环境不能使用开发/测试用的JWT密钥');
      }
      
      if (validatedEnv.DEBUG) {
        warnings.push('⚠️  生产环境建议关闭调试模式');
      }
    }
    
    // 输出警告
    if (warnings.length > 0) {
      console.log('\n警告信息:');
      warnings.forEach(warning => console.log(warning));
    }
    
    console.log(`✅ 环境变量验证通过 (${env} 环境)`);
    return validatedEnv;
    
  } catch (error) {
    console.error(`❌ 环境变量验证失败: ${error.message}`);
    console.error('\n请检查以下文件中的环境变量配置:');
    console.error('- .env.local (本地配置)');
    console.error(`- .env.${env} (环境特定配置)`);
    console.error('- .env (默认配置)');
    console.error('\n参考 .env.example 文件了解所需的环境变量');
    process.exit(1);
  }
}

/**
 * 获取验证后的环境配置
 */
export function getValidatedEnv() {
  return validateEnvironment();
}

/**
 * 检查环境配置完整性
 */
export function checkEnvironmentHealth() {
  const env = getValidatedEnv();
  const health = {
    status: 'healthy',
    environment: env.NODE_ENV,
    checks: {
      database: false,
      storage: false,
      security: false
    },
    issues: []
  };
  
  // 数据库连接检查
  if (env.DATABASE_URL) {
    health.checks.database = true;
  } else {
    health.issues.push('数据库连接配置缺失');
  }
  
  // 存储配置检查
  if (env.BLOB_READ_WRITE_TOKEN || env.ALIYUN_ACCESS_KEY_ID || env.UPLOAD_DIR) {
    health.checks.storage = true;
  } else {
    health.issues.push('文件存储配置缺失');
  }
  
  // 安全配置检查
  if (env.JWT_SECRET && env.ADMIN_PASSWORD) {
    health.checks.security = true;
  } else {
    health.issues.push('安全配置不完整');
  }
  
  if (health.issues.length > 0) {
    health.status = 'warning';
  }
  
  return health;
}
