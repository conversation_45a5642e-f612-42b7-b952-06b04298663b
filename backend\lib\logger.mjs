/**
 * 日志管理系统
 * 提供结构化日志记录和管理功能
 */

import { createWriteStream, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 日志级别定义
export const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
};

// 日志级别名称
const LEVEL_NAMES = {
  0: 'ERROR',
  1: 'WARN',
  2: 'INFO',
  3: 'DEBUG',
};

// 日志颜色（控制台输出）
const LEVEL_COLORS = {
  ERROR: '\x1b[31m', // 红色
  WARN: '\x1b[33m',  // 黄色
  INFO: '\x1b[36m',  // 青色
  DEBUG: '\x1b[37m', // 白色
};

const RESET_COLOR = '\x1b[0m';

/**
 * 日志记录器类
 */
export class Logger {
  constructor(options = {}) {
    this.level = this.parseLogLevel(options.level || process.env.LOG_LEVEL || 'info');
    this.enableConsole = options.enableConsole !== false;
    this.enableFile = options.enableFile !== false;
    this.logDir = options.logDir || join(process.cwd(), 'logs');
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 5;
    
    // 确保日志目录存在
    this.ensureLogDirectory();
    
    // 初始化文件流
    this.fileStreams = {};
    if (this.enableFile) {
      this.initFileStreams();
    }
    
    // 绑定方法
    this.error = this.error.bind(this);
    this.warn = this.warn.bind(this);
    this.info = this.info.bind(this);
    this.debug = this.debug.bind(this);
  }
  
  /**
   * 解析日志级别
   */
  parseLogLevel(level) {
    if (typeof level === 'number') {
      return level;
    }
    
    const upperLevel = level.toUpperCase();
    return LOG_LEVELS[upperLevel] !== undefined ? LOG_LEVELS[upperLevel] : LOG_LEVELS.INFO;
  }
  
  /**
   * 确保日志目录存在
   */
  ensureLogDirectory() {
    if (!existsSync(this.logDir)) {
      mkdirSync(this.logDir, { recursive: true });
    }
  }
  
  /**
   * 初始化文件流
   */
  initFileStreams() {
    const date = new Date().toISOString().split('T')[0];
    
    this.fileStreams = {
      combined: createWriteStream(join(this.logDir, `combined-${date}.log`), { flags: 'a' }),
      error: createWriteStream(join(this.logDir, `error-${date}.log`), { flags: 'a' }),
    };
  }
  
  /**
   * 格式化日志消息
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const levelName = LEVEL_NAMES[level];
    
    const logEntry = {
      timestamp,
      level: levelName,
      message,
      ...meta,
    };
    
    return logEntry;
  }
  
  /**
   * 格式化控制台输出
   */
  formatConsoleMessage(logEntry) {
    const { timestamp, level, message } = logEntry;
    const color = LEVEL_COLORS[level] || '';
    const time = new Date(timestamp).toLocaleTimeString();
    
    return `${color}[${time}] ${level}${RESET_COLOR}: ${message}`;
  }
  
  /**
   * 写入日志
   */
  writeLog(level, message, meta = {}) {
    // 检查日志级别
    if (level > this.level) {
      return;
    }
    
    const logEntry = this.formatMessage(level, message, meta);
    
    // 控制台输出
    if (this.enableConsole) {
      const consoleMessage = this.formatConsoleMessage(logEntry);
      
      if (level === LOG_LEVELS.ERROR) {
        console.error(consoleMessage);
      } else if (level === LOG_LEVELS.WARN) {
        console.warn(consoleMessage);
      } else {
        console.log(consoleMessage);
      }
      
      // 如果有错误对象，打印堆栈
      if (meta.error && meta.error.stack) {
        console.error(meta.error.stack);
      }
    }
    
    // 文件输出
    if (this.enableFile && this.fileStreams) {
      const fileMessage = JSON.stringify(logEntry) + '\n';
      
      // 写入综合日志
      this.fileStreams.combined.write(fileMessage);
      
      // 错误日志单独记录
      if (level === LOG_LEVELS.ERROR) {
        this.fileStreams.error.write(fileMessage);
      }
    }
  }
  
  /**
   * 错误日志
   */
  error(message, meta = {}) {
    this.writeLog(LOG_LEVELS.ERROR, message, meta);
  }
  
  /**
   * 警告日志
   */
  warn(message, meta = {}) {
    this.writeLog(LOG_LEVELS.WARN, message, meta);
  }
  
  /**
   * 信息日志
   */
  info(message, meta = {}) {
    this.writeLog(LOG_LEVELS.INFO, message, meta);
  }
  
  /**
   * 调试日志
   */
  debug(message, meta = {}) {
    this.writeLog(LOG_LEVELS.DEBUG, message, meta);
  }
  
  /**
   * 记录 HTTP 请求
   */
  logRequest(req, res, responseTime) {
    const { method, url, ip, headers } = req;
    const { statusCode } = res;
    
    const meta = {
      method,
      url,
      ip,
      statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: headers['user-agent'],
      referer: headers.referer,
    };
    
    const level = statusCode >= 400 ? LOG_LEVELS.WARN : LOG_LEVELS.INFO;
    this.writeLog(level, `${method} ${url} ${statusCode} ${responseTime}ms`, meta);
  }
  
  /**
   * 记录数据库操作
   */
  logDatabase(operation, table, duration, meta = {}) {
    this.info(`Database ${operation} on ${table}`, {
      operation,
      table,
      duration: `${duration}ms`,
      ...meta,
    });
  }
  
  /**
   * 记录安全事件
   */
  logSecurity(event, details = {}) {
    this.warn(`Security event: ${event}`, {
      event,
      timestamp: new Date().toISOString(),
      ...details,
    });
  }
  
  /**
   * 记录性能指标
   */
  logPerformance(metric, value, unit = 'ms') {
    this.info(`Performance: ${metric} = ${value}${unit}`, {
      metric,
      value,
      unit,
      timestamp: new Date().toISOString(),
    });
  }
  
  /**
   * 关闭日志记录器
   */
  close() {
    if (this.fileStreams) {
      Object.values(this.fileStreams).forEach(stream => {
        if (stream && typeof stream.end === 'function') {
          stream.end();
        }
      });
    }
  }
}

/**
 * 创建默认日志记录器实例
 */
export const logger = new Logger({
  level: process.env.LOG_LEVEL || 'info',
  enableConsole: process.env.NODE_ENV !== 'test',
  enableFile: process.env.NODE_ENV !== 'test',
});

/**
 * Express 中间件：请求日志记录
 */
export function requestLogger() {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // 记录请求开始
    logger.debug(`Request started: ${req.method} ${req.url}`, {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    
    // 监听响应结束
    res.on('finish', () => {
      const responseTime = Date.now() - startTime;
      logger.logRequest(req, res, responseTime);
    });
    
    next();
  };
}

/**
 * 错误处理中间件
 */
export function errorLogger() {
  return (err, req, res, next) => {
    logger.error(`Unhandled error: ${err.message}`, {
      error: err,
      stack: err.stack,
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    
    next(err);
  };
}

/**
 * 进程退出时清理
 */
process.on('exit', () => {
  logger.close();
});

process.on('SIGINT', () => {
  logger.info('Received SIGINT, closing logger...');
  logger.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, closing logger...');
  logger.close();
  process.exit(0);
});

export default logger;
