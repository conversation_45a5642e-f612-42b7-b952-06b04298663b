// 环境变量验证和配置
import { validateEnvironment } from './lib/env-validation.mjs';
const env = validateEnvironment();

import express from 'express';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import { del as vercelBlobDel } from '@vercel/blob';
import { handleUpload } from '@vercel/blob/client';
import {
  validateAdminCredentials,
  generateToken,
  authenticateToken,
  requireAdmin,
  sessionManager
} from './lib/auth.mjs';

const app = express();
const prisma = new PrismaClient();

// CORS 配置
const corsOptions = {
  origin: env.CORS_ORIGIN === '*' ? true : env.CORS_ORIGIN.split(','),
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
app.use(express.json({ limit: `${env.MAX_FILE_SIZE}b` }));

// --- Authentication Routes ---

// 管理员登录
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码不能为空' });
    }

    const isValid = await validateAdminCredentials(username, password);

    if (!isValid) {
      return res.status(401).json({ error: '用户名或密码错误' });
    }

    // 生成 JWT Token
    const token = generateToken({
      username,
      role: 'admin',
      loginTime: new Date().toISOString()
    });

    // 创建会话
    const sessionId = sessionManager.createSession(username, { role: 'admin' });

    res.json({
      success: true,
      token,
      sessionId,
      user: { username, role: 'admin' }
    });

  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ error: '登录失败，请稍后重试' });
  }
});

// 验证登录状态
app.get('/api/auth/verify', authenticateToken, (req, res) => {
  res.json({
    valid: true,
    user: req.user
  });
});

// 登出
app.post('/api/auth/logout', authenticateToken, (req, res) => {
  const sessionId = req.headers['x-session-id'];
  if (sessionId) {
    sessionManager.destroySession(sessionId);
  }
  res.json({ success: true, message: '已成功登出' });
});

// --- File Upload Routes ---

// This new endpoint will handle generating a signed URL for client-side uploads.
app.post('/api/upload', async (req, res) => {
  try {
    const jsonResponse = await handleUpload({
      body: req.body,
      request: req,
      onBeforeGenerateToken: async (pathname) => {
        // The token is read from the BLOB_READ_WRITE_TOKEN environment variable that
        // is automatically set by Vercel when a Blob store is connected.
        return {
          // Can add metadata to the token payload, like a user ID
        };
      },
      onUploadCompleted: async ({ blob, tokenPayload }) => {
        // This callback is executed after the file is uploaded to Vercel Blob.
        console.log('Blob upload completed!', blob, tokenPayload);
      },
    });

    res.status(200).json(jsonResponse);
  } catch (error) {
    console.error('An error occurred during upload handling:', error);
    res.status(400).json({ error: error.message });
  }
});

// GET all models
app.get('/api/models', async (req, res) => {
    try {
        const models = await prisma.model.findMany({
            orderBy: { createdAt: 'desc' },
        });
        res.json(models);
    } catch (error) {
        console.error('Failed to fetch models:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// GET a single model by ID
app.get('/api/models/:id', async (req, res) => {
    try {
        const model = await prisma.model.findUnique({ where: { id: req.params.id } });
        if (!model) return res.status(404).json({ error: 'Model not found' });
        res.json(model);
    } catch (error) {
        console.error(`Failed to fetch model ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// POST a new model - now expects URLs from client-side upload
app.post('/api/models', async (req, res) => {
    const { name, fileType, size, url, thumbnailUrl } = req.body;

    if (!name || !url) {
        return res.status(400).json({ error: 'Name and model URL are required.' });
    }

    try {
        const newModel = await prisma.model.create({
            data: {
                name,
                fileType,
                size: String(size),
                filePath: url,
                url: url,
                thumbnail: thumbnailUrl,
            },
        });
        res.status(201).json(newModel);
    } catch (error) {
        console.error('Failed to create model record:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// DELETE a model
app.delete('/api/models/:id', async (req, res) => {
    try {
        const model = await prisma.model.findUnique({ where: { id: req.params.id } });
        if (!model) return res.status(404).json({ error: 'Model not found' });

        // Delete files from Vercel Blob storage
        if (model.url) await vercelBlobDel(model.url);
        if (model.thumbnail) await vercelBlobDel(model.thumbnail);

        // Delete from database
        await prisma.model.delete({ where: { id: req.params.id } });

        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete model ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});


// GET all materials
app.get('/api/materials', async (req, res) => {
    try {
        const materials = await prisma.material.findMany({
            orderBy: { createdAt: 'desc' },
        });
        res.json(materials);
    } catch (error) {
        console.error('Failed to fetch materials:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// POST a new material - now expects thumbnail URL from client-side upload
app.post('/api/materials', async (req, res) => {
    const { name, color, metalness, roughness, glass, thumbnailUrl } = req.body;

    try {
        const newMaterial = await prisma.material.create({
            data: {
                name,
                color,
                metalness: parseFloat(metalness) || 0,
                roughness: parseFloat(roughness) || 0,
                glass: parseFloat(glass) || 0,
                thumbnail: thumbnailUrl,
            },
        });
        res.status(201).json(newMaterial);
    } catch (error) {
        console.error('Failed to add material:', error);
        res.status(500).json({ error: 'Failed to add material.' });
    }
});

// DELETE a material
app.delete('/api/materials/:id', async (req, res) => {
    try {
        const material = await prisma.material.findUnique({ where: { id: req.params.id } });
        if (material && material.thumbnail) {
            await vercelBlobDel(material.thumbnail);
        }
        await prisma.material.delete({ where: { id: req.params.id } });
        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete material ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// --- Export and Listen ---

// This allows Vercel to import the app as a serverless function.
export default app;

// 服务器启动逻辑
// 在非 Vercel 环境中启动服务器（本地开发或阿里云部署）
if (env.NODE_ENV === 'development' || !process.env.VERCEL) {
    app.listen(env.PORT, () => {
        console.log(`🚀 服务器已启动`);
        console.log(`📍 环境: ${env.NODE_ENV}`);
        console.log(`🌐 地址: http://localhost:${env.PORT}`);
        console.log(`📊 调试模式: ${env.DEBUG ? '开启' : '关闭'}`);
        console.log(`📝 日志级别: ${env.LOG_LEVEL}`);

        if (env.NODE_ENV === 'development') {
            console.log(`🔧 API 文档: ${env.ENABLE_API_DOCS ? '开启' : '关闭'}`);
        }
    });
}
