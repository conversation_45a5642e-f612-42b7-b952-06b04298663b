version: '3.8'

services:
  # 开发环境应用服务
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: huitong-material-dev
    ports:
      - "3001:3001"
      - "5173:5173"  # Vite 开发服务器
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=******************************************/huitong_material_dev
      - POSTGRES_PRISMA_URL=******************************************/huitong_material_dev
      - POSTGRES_URL_NON_POOLING=******************************************/huitong_material_dev
      - DEBUG=true
      - LOG_LEVEL=debug
    env_file:
      - .env.development
    depends_on:
      db-dev:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
      - uploads-dev:/app/uploads
    networks:
      - huitong-dev-network
    restart: unless-stopped
    command: npm run dev

  # 开发数据库服务
  db-dev:
    image: postgres:15-alpine
    container_name: huitong-material-db-dev
    environment:
      POSTGRES_DB: huitong_material_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"  # 使用不同端口避免冲突
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - huitong-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 开发 Redis 服务
  redis-dev:
    image: redis:7-alpine
    container_name: huitong-material-redis-dev
    ports:
      - "6380:6379"  # 使用不同端口避免冲突
    volumes:
      - redis_dev_data:/data
    networks:
      - huitong-dev-network
    restart: unless-stopped

  # 数据库管理工具
  adminer:
    image: adminer
    container_name: huitong-material-adminer
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: db-dev
    depends_on:
      - db-dev
    networks:
      - huitong-dev-network
    restart: unless-stopped

  # 邮件测试服务
  mailhog:
    image: mailhog/mailhog
    container_name: huitong-material-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - huitong-dev-network
    restart: unless-stopped

# 开发环境数据卷
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  uploads-dev:
    driver: local

# 开发环境网络
networks:
  huitong-dev-network:
    driver: bridge
