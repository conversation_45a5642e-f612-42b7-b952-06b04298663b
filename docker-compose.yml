version: '3.8'

services:
  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: huitong-material-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=**************************************/huitong_material
      - POSTGRES_PRISMA_URL=**************************************/huitong_material
      - POSTGRES_URL_NON_POOLING=**************************************/huitong_material
    env_file:
      - .env.local
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    networks:
      - huitong-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 数据库服务
  db:
    image: postgres:15-alpine
    container_name: huitong-material-db
    environment:
      POSTGRES_DB: huitong_material
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - huitong-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: huitong-material-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - huitong-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: huitong-material-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - uploads:/var/www/uploads:ro
    depends_on:
      - app
    networks:
      - huitong-network
    restart: unless-stopped

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local

# 网络
networks:
  huitong-network:
    driver: bridge
