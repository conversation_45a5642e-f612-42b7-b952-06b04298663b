# 部署指南

本指南详细说明了如何在不同环境中部署会通智能色彩云库项目，包括 Vercel、阿里云和本地部署。

## 📋 目录

- [部署概览](#部署概览)
- [Vercel 部署](#vercel-部署)
- [阿里云部署](#阿里云部署)
- [本地部署](#本地部署)
- [环境迁移](#环境迁移)
- [监控和维护](#监控和维护)

## 🌐 部署概览

### 支持的部署方式

| 部署方式 | 适用场景 | 优势 | 劣势 |
|----------|----------|------|------|
| **Vercel** | 快速原型、演示 | 零配置、自动扩展 | 成本较高、功能限制 |
| **阿里云** | 生产环境 | 成本可控、功能完整 | 需要运维知识 |
| **本地部署** | 开发、测试 | 完全控制、成本最低 | 需要自行维护 |

### 架构组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (Node.js)  │    │  数据库 (PostgreSQL) │
│                 │    │                 │    │                 │
│ • Vite 构建     │    │ • Express 服务器 │    │ • Prisma ORM    │
│ • 静态资源      │    │ • API 接口      │    │ • 数据存储      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   文件存储       │
                    │                 │
                    │ • Vercel Blob   │
                    │ • 阿里云 OSS    │
                    │ • 本地存储      │
                    └─────────────────┘
```

## 🚀 Vercel 部署

### 前置条件

- GitHub 账户
- Vercel 账户
- Neon 数据库账户（或其他 PostgreSQL 服务）

### 部署步骤

#### 1. 准备代码仓库

```bash
# 确保代码已推送到 GitHub
git add .
git commit -m "准备 Vercel 部署"
git push origin main
```

#### 2. 配置 Vercel 项目

1. 登录 [Vercel Dashboard](https://vercel.com/dashboard)
2. 点击 "New Project"
3. 导入 GitHub 仓库
4. 配置构建设置：

```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm install",
  "devCommand": "npm run dev"
}
```

#### 3. 配置环境变量

在 Vercel 项目设置中添加以下环境变量：

```bash
# 应用配置
NODE_ENV=production
VITE_API_BASE_URL=/api
VITE_SERVER_URL=
VITE_ENV=production

# 数据库配置 (Neon)
POSTGRES_PRISMA_URL=********************************************
POSTGRES_URL_NON_POOLING=********************************************
DATABASE_URL=********************************************

# Vercel Blob 存储
BLOB_READ_WRITE_TOKEN=vercel_blob_rw_xxxxx

# 安全配置
JWT_SECRET=your_production_jwt_secret
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password
SESSION_SECRET=your_session_secret
CORS_ORIGIN=https://your-domain.vercel.app
```

#### 4. 配置域名（可选）

1. 在 Vercel 项目设置中添加自定义域名
2. 配置 DNS 记录
3. 更新 `CORS_ORIGIN` 环境变量

### Vercel 特定配置

确保 `vercel.json` 文件配置正确：

```json
{
  "version": 2,
  "builds": [
    {
      "src": "api/**/*.mjs",
      "use": "@vercel/node"
    },
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": { "distDir": "dist" }
    }
  ],
  "rewrites": [
    {
      "source": "/api/(.*)",
      "destination": "/api/index.mjs"
    },
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

## ☁️ 阿里云部署

### 前置条件

- 阿里云 ECS 实例
- 阿里云 RDS PostgreSQL 数据库
- 阿里云 OSS 存储桶
- 域名和 SSL 证书

### 部署步骤

#### 1. 服务器准备

```bash
# 连接到 ECS 实例
ssh root@your-server-ip

# 更新系统
yum update -y

# 安装 Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
yum install -y nodejs

# 安装 PM2
npm install -g pm2

# 安装 Nginx
yum install -y nginx

# 安装 Git
yum install -y git
```

#### 2. 部署应用

```bash
# 克隆代码
git clone https://github.com/your-username/huitong-material.git
cd huitong-material

# 切换到生产分支
git checkout production  # 或适当的分支

# 安装依赖
npm install

# 配置环境变量
cp .env.production .env.local
# 编辑 .env.local 填入实际配置

# 构建前端
npm run build

# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma db push
```

#### 3. 配置 PM2

创建 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [{
    name: 'huitong-material',
    script: 'backend/server.mjs',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

启动应用：

```bash
# 创建日志目录
mkdir logs

# 启动应用
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

#### 4. 配置 Nginx

创建 `/etc/nginx/conf.d/huitong-material.conf`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # 静态文件
    location / {
        root /path/to/huitong-material/dist;
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 文件上传
    location /uploads {
        alias /path/to/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启动 Nginx：

```bash
# 测试配置
nginx -t

# 启动 Nginx
systemctl start nginx
systemctl enable nginx
```

#### 5. 配置阿里云 OSS

```bash
# 安装阿里云 CLI（可选）
wget https://aliyuncli.alicdn.com/aliyun-cli-linux-latest-amd64.tgz
tar xzvf aliyun-cli-linux-latest-amd64.tgz
sudo mv aliyun /usr/local/bin/

# 配置访问密钥
aliyun configure
```

在 `.env.local` 中配置 OSS：

```bash
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_OSS_REGION=oss-cn-hangzhou
ALIYUN_OSS_BUCKET=your_bucket_name
ALIYUN_OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
```

## 💻 本地部署

### 开发环境

```bash
# 克隆项目
git clone https://github.com/your-username/huitong-material.git
cd huitong-material

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local

# 启动数据库（使用 Docker）
docker run --name postgres \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=huitong_material_dev \
  -p 5432:5432 \
  -d postgres:15

# 运行数据库迁移
npx prisma db push

# 启动开发服务器
npm run dev
```

### 生产环境

```bash
# 构建项目
npm run build

# 启动生产服务器
npm start
```

## 🔄 环境迁移

### 从 Vercel 迁移到阿里云

#### 1. 数据迁移

```bash
# 导出 Neon 数据库
pg_dump $NEON_DATABASE_URL > backup.sql

# 导入到阿里云 RDS
psql $ALIYUN_RDS_URL < backup.sql
```

#### 2. 文件迁移

```bash
# 使用阿里云 OSS 工具迁移 Vercel Blob 文件
# 或编写迁移脚本
```

#### 3. 配置更新

1. 更新环境变量
2. 修改 DNS 记录
3. 测试功能完整性

### 迁移检查清单

- [ ] 数据库数据完整性
- [ ] 文件存储访问正常
- [ ] 环境变量配置正确
- [ ] SSL 证书配置
- [ ] DNS 解析正确
- [ ] 监控和日志配置
- [ ] 备份策略设置

## 📊 监控和维护

### 日志管理

```bash
# PM2 日志
pm2 logs

# Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 应用日志
tail -f logs/combined.log
```

### 性能监控

```bash
# 系统资源
htop
df -h
free -h

# 应用状态
pm2 status
pm2 monit
```

### 备份策略

```bash
# 数据库备份
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# 文件备份
tar -czf uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz uploads/

# 自动化备份脚本
crontab -e
# 添加：0 2 * * * /path/to/backup-script.sh
```

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install

# 构建前端
npm run build

# 重启应用
pm2 restart huitong-material

# 重载 Nginx（如有配置更改）
nginx -s reload
```

---

📝 **注意**: 部署前请仔细阅读 [环境配置指南](./ENVIRONMENT_SETUP.md) 确保所有环境变量正确配置。
