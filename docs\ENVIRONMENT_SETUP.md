# 环境变量配置指南

本文档详细说明了项目的环境变量配置，包括开发、预发布和生产环境的设置。

## 📋 目录

- [快速开始](#快速开始)
- [环境文件结构](#环境文件结构)
- [必需的环境变量](#必需的环境变量)
- [可选的环境变量](#可选的环境变量)
- [环境特定配置](#环境特定配置)
- [安全最佳实践](#安全最佳实践)
- [故障排除](#故障排除)

## 🚀 快速开始

### 1. 复制环境模板

```bash
# 复制示例文件
cp .env.example .env.local

# 或使用自动化设置脚本
npm run env:setup
```

### 2. 配置必需变量

编辑 `.env.local` 文件，填入以下必需变量：

```bash
# 数据库连接
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# 安全配置
JWT_SECRET=your_super_secret_jwt_key_here
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_admin_password
```

### 3. 验证配置

```bash
# 验证环境变量
npm run env:validate

# 检查系统健康状态
npm run env:health
```

## 📁 环境文件结构

```
project-root/
├── .env.example          # 环境变量模板（提交到版本控制）
├── .env.development      # 开发环境默认配置（提交到版本控制）
├── .env.staging          # 预发布环境模板（提交到版本控制）
├── .env.production       # 生产环境模板（提交到版本控制）
├── .env.local           # 本地覆盖配置（不提交到版本控制）
└── .env                 # 当前环境配置（不提交到版本控制）
```

### 加载优先级

环境变量按以下优先级加载（高优先级覆盖低优先级）：

1. `.env.local` - 本地覆盖（最高优先级）
2. `.env.${NODE_ENV}` - 环境特定配置
3. `.env` - 默认配置

## 🔧 必需的环境变量

### 应用基础配置

| 变量名 | 描述 | 示例值 | 必需 |
|--------|------|--------|------|
| `NODE_ENV` | 运行环境 | `development`, `staging`, `production` | ✅ |
| `PORT` | 服务器端口 | `3001` | ✅ |

### 前端配置

| 变量名 | 描述 | 示例值 | 必需 |
|--------|------|--------|------|
| `VITE_API_BASE_URL` | API 基础 URL | `http://localhost:3001/api` | ✅ |
| `VITE_SERVER_URL` | 服务器 URL | `http://localhost:3001` | ✅ |
| `VITE_ENV` | 前端环境标识 | `development` | ✅ |

### 数据库配置

| 变量名 | 描述 | 示例值 | 必需 |
|--------|------|--------|------|
| `DATABASE_URL` | 数据库连接 URL | `postgresql://user:pass@localhost:5432/db` | ✅ |
| `POSTGRES_PRISMA_URL` | Prisma 连接池 URL | 同上 | ✅ |
| `POSTGRES_URL_NON_POOLING` | Prisma 直连 URL | 同上 | ✅ |

### 安全配置

| 变量名 | 描述 | 示例值 | 必需 |
|--------|------|--------|------|
| `JWT_SECRET` | JWT 签名密钥 | 64位随机字符串 | ✅ |
| `ADMIN_USERNAME` | 管理员用户名 | `admin` | ✅ |
| `ADMIN_PASSWORD` | 管理员密码 | 强密码 | ✅ |

## ⚙️ 可选的环境变量

### 文件存储配置

#### Vercel Blob 存储
```bash
BLOB_READ_WRITE_TOKEN=vercel_blob_token_here
```

#### 阿里云 OSS 存储
```bash
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_OSS_REGION=oss-cn-hangzhou
ALIYUN_OSS_BUCKET=your_bucket_name
ALIYUN_OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
```

#### 本地文件存储
```bash
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=52428800  # 50MB in bytes
```

### 开发工具配置

| 变量名 | 描述 | 默认值 | 可选值 |
|--------|------|--------|--------|
| `DEBUG` | 调试模式 | `false` | `true`, `false` |
| `LOG_LEVEL` | 日志级别 | `info` | `error`, `warn`, `info`, `debug` |
| `ENABLE_API_DOCS` | API 文档 | `false` | `true`, `false` |

### 安全和网络配置

```bash
SESSION_SECRET=your_session_secret_here
CORS_ORIGIN=https://yourdomain.com  # 生产环境域名
```

## 🌍 环境特定配置

### 开发环境 (development)

```bash
NODE_ENV=development
PORT=3001
VITE_API_BASE_URL=http://localhost:3001/api
VITE_SERVER_URL=http://localhost:3001
DEBUG=true
LOG_LEVEL=debug
ENABLE_API_DOCS=true
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:5173
```

### 预发布环境 (staging)

```bash
NODE_ENV=staging
PORT=3001
VITE_API_BASE_URL=/api
VITE_SERVER_URL=
DEBUG=true
LOG_LEVEL=info
ENABLE_API_DOCS=true
CORS_ORIGIN=https://staging.yourdomain.com
```

### 生产环境 (production)

```bash
NODE_ENV=production
PORT=3000
VITE_API_BASE_URL=/api
VITE_SERVER_URL=
DEBUG=false
LOG_LEVEL=warn
ENABLE_API_DOCS=false
CORS_ORIGIN=https://yourdomain.com
```

## 🔒 安全最佳实践

### 1. 密钥管理

- **使用强随机密钥**: JWT_SECRET 至少 64 位随机字符
- **定期轮换密钥**: 建议每 3-6 个月更换一次
- **环境隔离**: 不同环境使用不同的密钥

### 2. 密码安全

- **强密码策略**: 至少 12 位，包含大小写字母、数字和特殊字符
- **避免默认密码**: 生产环境必须更改默认密码
- **密码哈希**: 生产环境使用 bcrypt 哈希存储密码

### 3. 文件安全

- **版本控制**: 永远不要提交包含真实密钥的 `.env` 文件
- **文件权限**: 设置适当的文件权限 (600 或 644)
- **备份安全**: 安全地备份和存储环境配置

### 4. 网络安全

- **CORS 配置**: 生产环境设置具体的域名，避免使用 `*`
- **HTTPS**: 生产环境强制使用 HTTPS
- **防火墙**: 配置适当的防火墙规则

## 🛠️ 故障排除

### 常见问题

#### 1. 环境变量未加载

**症状**: 应用启动时提示环境变量缺失

**解决方案**:
```bash
# 检查文件是否存在
ls -la .env*

# 验证环境变量
npm run env:validate

# 检查文件内容
cat .env.local
```

#### 2. 数据库连接失败

**症状**: 数据库连接错误

**解决方案**:
```bash
# 检查数据库 URL 格式
echo $DATABASE_URL

# 测试数据库连接
npx prisma db pull

# 检查数据库服务状态
pg_isready -h localhost -p 5432
```

#### 3. 认证失败

**症状**: 登录时提示认证错误

**解决方案**:
```bash
# 检查 JWT 密钥
echo $JWT_SECRET

# 验证管理员凭据
echo "Username: $ADMIN_USERNAME"
echo "Password length: ${#ADMIN_PASSWORD}"

# 重新生成密钥
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### 调试命令

```bash
# 显示所有环境变量
npm run env:health

# 检查配置一致性
npm run env:check

# 验证环境配置
npm run env:validate

# 启动调试模式
DEBUG=true npm run dev
```

### 获取帮助

如果遇到问题，请：

1. 检查本文档的故障排除部分
2. 查看应用日志输出
3. 验证环境变量配置
4. 检查网络和防火墙设置
5. 联系技术支持团队

## 📚 相关文档

- [部署指南](./DEPLOYMENT_GUIDE.md)
- [安全配置指南](./SECURITY_GUIDE.md)
- [API 文档](./API_DOCUMENTATION.md)

---

📝 **注意**: 本文档会随着项目发展持续更新。请定期查看最新版本。
