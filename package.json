{"name": "huitong-material-vercel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently --kill-others-on-fail --names \"frontend,backend\" -c \"bgBlue.bold,bgGreen.bold\" \"npm:dev:frontend\" \"npm:dev:backend\"", "dev:frontend": "vite", "dev:backend": "nodemon backend/server.mjs", "build": "prisma generate && vite build", "start": "node backend/server.mjs", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:unused": "ts-prune", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "prepare": "husky install", "preview": "vite preview", "env:setup": "node scripts/env-setup.mjs", "env:check": "node scripts/env-setup.mjs", "env:validate": "node -e \"import('./backend/lib/env-validation.mjs').then(m => m.validateEnvironment())\"", "env:health": "node -e \"import('./backend/lib/env-validation.mjs').then(m => console.log(JSON.stringify(m.checkEnvironmentHealth(), null, 2)))\"", "migrate": "node scripts/migration-helper.mjs", "migrate:validate": "node scripts/migration-helper.mjs"}, "dependencies": {"@prisma/client": "^5.17.0", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@vercel/blob": "^0.23.4", "@vercel/postgres": "^0.9.0", "ali-oss": "^6.20.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "multer": "^2.0.1", "pg": "^8.12.0", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "three": "^0.177.0", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/multer": "^1.4.13", "@types/node": "^20.14.10", "@types/pg": "^8.11.6", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/three": "^0.166.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^8.2.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "husky": "^9.0.10", "json-server": "^1.0.0-beta.3", "lint-staged": "^15.2.3", "nodemon": "^3.1.0", "prettier": "^3.6.2", "prisma": "^5.17.0", "ts-prune": "^0.10.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["prettier --write", "eslint --fix"], "*.{json,md,yml,yaml}": ["prettier --write"], "*.{css,scss,less}": ["prettier --write"], "*.{mjs}": ["prettier --write", "eslint --fix"]}}