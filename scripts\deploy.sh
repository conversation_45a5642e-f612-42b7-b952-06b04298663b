#!/bin/bash

# 部署脚本
# 用法: ./scripts/deploy.sh [environment] [options]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
部署脚本使用说明

用法: $0 [ENVIRONMENT] [OPTIONS]

环境:
  development    部署到开发环境
  staging        部署到预发布环境
  production     部署到生产环境

选项:
  --skip-checks     跳过预部署检查
  --skip-backup     跳过备份
  --force           强制部署（跳过确认）
  --help           显示此帮助信息

示例:
  $0 staging
  $0 production --skip-backup
  $0 development --force

EOF
}

# 默认参数
ENVIRONMENT=""
SKIP_CHECKS=false
SKIP_BACKUP=false
FORCE=false

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --skip-checks)
            SKIP_CHECKS=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ -z "$ENVIRONMENT" ]]; then
    log_error "请指定部署环境"
    show_help
    exit 1
fi

log_info "开始部署到 $ENVIRONMENT 环境"

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    local missing_tools=()
    
    if ! command -v node &> /dev/null; then
        missing_tools+=("node")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_tools+=("npm")
    fi
    
    if ! command -v git &> /dev/null; then
        missing_tools+=("git")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        exit 1
    fi
    
    log_success "依赖工具检查通过"
}

# 预部署检查
pre_deploy_checks() {
    if [[ "$SKIP_CHECKS" == true ]]; then
        log_warning "跳过预部署检查"
        return
    fi
    
    log_info "执行预部署检查..."
    
    # 检查 Git 状态
    if [[ -n $(git status --porcelain) ]]; then
        log_error "存在未提交的更改，请先提交所有更改"
        exit 1
    fi
    
    # 安装依赖
    log_info "安装依赖..."
    npm ci
    
    # 代码质量检查
    log_info "运行代码质量检查..."
    npm run lint
    npm run format:check
    npm run type-check
    
    # 环境变量验证
    log_info "验证环境变量..."
    if [[ ! -f ".env.$ENVIRONMENT" ]]; then
        log_error "环境配置文件 .env.$ENVIRONMENT 不存在"
        exit 1
    fi
    
    cp ".env.$ENVIRONMENT" .env.local
    npm run env:validate
    
    # 构建测试
    log_info "测试构建..."
    npm run build
    
    log_success "预部署检查通过"
}

# 备份
create_backup() {
    if [[ "$SKIP_BACKUP" == true ]]; then
        log_warning "跳过备份"
        return
    fi
    
    log_info "创建备份..."
    
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)_$ENVIRONMENT"
    mkdir -p "$backup_dir"
    
    # 备份数据库（如果是生产环境）
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "备份生产数据库..."
        # 这里添加实际的数据库备份命令
        # pg_dump $DATABASE_URL > "$backup_dir/database.sql"
    fi
    
    # 备份上传文件
    if [[ -d "uploads" ]]; then
        log_info "备份上传文件..."
        cp -r uploads "$backup_dir/"
    fi
    
    log_success "备份完成: $backup_dir"
}

# 部署确认
confirm_deployment() {
    if [[ "$FORCE" == true ]]; then
        return
    fi
    
    echo
    log_warning "即将部署到 $ENVIRONMENT 环境"
    echo "请确认以下信息:"
    echo "- 环境: $ENVIRONMENT"
    echo "- 分支: $(git branch --show-current)"
    echo "- 提交: $(git rev-parse --short HEAD)"
    echo "- 时间: $(date)"
    echo
    
    read -p "确认部署? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 执行部署
deploy() {
    log_info "开始部署..."
    
    # 设置环境变量
    cp ".env.$ENVIRONMENT" .env.local
    
    # 安装生产依赖
    log_info "安装生产依赖..."
    npm ci --only=production
    
    # 构建项目
    log_info "构建项目..."
    npm run build
    
    # 数据库迁移
    log_info "运行数据库迁移..."
    npx prisma db push
    
    # 根据环境执行不同的部署策略
    case $ENVIRONMENT in
        development)
            deploy_development
            ;;
        staging)
            deploy_staging
            ;;
        production)
            deploy_production
            ;;
    esac
    
    log_success "部署完成"
}

# 开发环境部署
deploy_development() {
    log_info "部署到开发环境..."
    
    # 启动开发服务器
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.dev.yml up -d
    else
        npm run dev &
    fi
}

# 预发布环境部署
deploy_staging() {
    log_info "部署到预发布环境..."
    
    # 使用 Docker Compose 部署
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        log_warning "Docker Compose 未安装，使用 PM2 部署"
        npm install -g pm2
        pm2 start ecosystem.config.js --env staging
    fi
}

# 生产环境部署
deploy_production() {
    log_info "部署到生产环境..."
    
    # 额外的生产环境检查
    log_info "执行生产环境安全检查..."
    
    # 检查构建产物
    if grep -r "localhost" dist/ 2>/dev/null; then
        log_error "构建产物中发现 localhost 引用"
        exit 1
    fi
    
    # 使用 PM2 部署
    if command -v pm2 &> /dev/null; then
        pm2 start ecosystem.config.js --env production
        pm2 save
    else
        log_error "PM2 未安装，无法部署到生产环境"
        exit 1
    fi
}

# 部署后验证
post_deploy_verification() {
    log_info "执行部署后验证..."
    
    # 等待服务启动
    sleep 10
    
    # 健康检查
    local health_url="http://localhost:3000/health"
    if [[ "$ENVIRONMENT" == "development" ]]; then
        health_url="http://localhost:3001/health"
    fi
    
    if command -v curl &> /dev/null; then
        if curl -f "$health_url" &> /dev/null; then
            log_success "健康检查通过"
        else
            log_error "健康检查失败"
            exit 1
        fi
    else
        log_warning "curl 未安装，跳过健康检查"
    fi
    
    log_success "部署后验证完成"
}

# 主函数
main() {
    log_info "🚀 会通智能色彩云库部署脚本"
    echo
    
    check_dependencies
    pre_deploy_checks
    create_backup
    confirm_deployment
    deploy
    post_deploy_verification
    
    echo
    log_success "🎉 部署成功完成！"
    log_info "环境: $ENVIRONMENT"
    log_info "时间: $(date)"
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        log_info "访问地址: http://localhost:3001"
    elif [[ "$ENVIRONMENT" == "staging" ]]; then
        log_info "访问地址: https://staging.yourdomain.com"
    else
        log_info "访问地址: https://yourdomain.com"
    fi
}

# 执行主函数
main "$@"
