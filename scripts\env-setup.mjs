#!/usr/bin/env node

/**
 * 环境配置设置脚本
 * 帮助开发者快速设置不同环境的配置
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { createInterface } from 'readline';
import { generateSecretKey } from '../backend/lib/auth.mjs';

const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

/**
 * 生成强密码
 */
function generateStrongPassword(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 环境配置模板
 */
const ENV_TEMPLATES = {
  development: {
    NODE_ENV: 'development',
    PORT: '3001',
    VITE_API_BASE_URL: 'http://localhost:3001/api',
    VITE_SERVER_URL: 'http://localhost:3001',
    VITE_ENV: 'development',
    DEBUG: 'true',
    LOG_LEVEL: 'debug',
    ENABLE_API_DOCS: 'true',
    CORS_ORIGIN: 'http://localhost:3000,http://localhost:3001,http://localhost:5173'
  },
  staging: {
    NODE_ENV: 'staging',
    PORT: '3001',
    VITE_API_BASE_URL: '/api',
    VITE_SERVER_URL: '',
    VITE_ENV: 'staging',
    DEBUG: 'true',
    LOG_LEVEL: 'info',
    ENABLE_API_DOCS: 'true'
  },
  production: {
    NODE_ENV: 'production',
    PORT: '3000',
    VITE_API_BASE_URL: '/api',
    VITE_SERVER_URL: '',
    VITE_ENV: 'production',
    DEBUG: 'false',
    LOG_LEVEL: 'warn',
    ENABLE_API_DOCS: 'false'
  }
};

/**
 * 创建环境配置文件
 */
async function createEnvFile(environment) {
  console.log(`\n🔧 设置 ${environment} 环境配置...\n`);
  
  const template = ENV_TEMPLATES[environment];
  const envVars = { ...template };
  
  // 数据库配置
  console.log('📊 数据库配置:');
  const dbHost = await question(`数据库主机 (默认: localhost): `) || 'localhost';
  const dbPort = await question(`数据库端口 (默认: 5432): `) || '5432';
  const dbName = await question(`数据库名称 (默认: huitong_material_${environment}): `) || `huitong_material_${environment}`;
  const dbUser = await question(`数据库用户名: `);
  const dbPassword = await question(`数据库密码: `);
  
  if (dbUser && dbPassword) {
    const dbUrl = `postgresql://${dbUser}:${dbPassword}@${dbHost}:${dbPort}/${dbName}`;
    envVars.POSTGRES_PRISMA_URL = dbUrl;
    envVars.POSTGRES_URL_NON_POOLING = dbUrl;
    envVars.DATABASE_URL = dbUrl;
  }
  
  // 安全配置
  console.log('\n🔐 安全配置:');
  const jwtSecret = await question(`JWT 密钥 (留空自动生成): `) || generateSecretKey(64);
  const adminUsername = await question(`管理员用户名 (默认: admin): `) || 'admin';
  const adminPassword = await question(`管理员密码 (留空自动生成): `) || generateStrongPassword(16);
  const sessionSecret = await question(`会话密钥 (留空自动生成): `) || generateSecretKey(64);
  
  envVars.JWT_SECRET = jwtSecret;
  envVars.ADMIN_USERNAME = adminUsername;
  envVars.ADMIN_PASSWORD = adminPassword;
  envVars.SESSION_SECRET = sessionSecret;
  
  // 文件存储配置
  console.log('\n📁 文件存储配置:');
  const storageType = await question(`存储类型 (1: 本地, 2: Vercel Blob, 3: 阿里云OSS): `);
  
  if (storageType === '2') {
    const blobToken = await question(`Vercel Blob Token: `);
    envVars.BLOB_READ_WRITE_TOKEN = blobToken;
  } else if (storageType === '3') {
    const accessKeyId = await question(`阿里云 Access Key ID: `);
    const accessKeySecret = await question(`阿里云 Access Key Secret: `);
    const region = await question(`阿里云 OSS 区域 (默认: oss-cn-hangzhou): `) || 'oss-cn-hangzhou';
    const bucket = await question(`阿里云 OSS Bucket: `);
    
    envVars.ALIYUN_ACCESS_KEY_ID = accessKeyId;
    envVars.ALIYUN_ACCESS_KEY_SECRET = accessKeySecret;
    envVars.ALIYUN_OSS_REGION = region;
    envVars.ALIYUN_OSS_BUCKET = bucket;
    envVars.ALIYUN_OSS_ENDPOINT = `https://${region}.aliyuncs.com`;
  } else {
    const uploadDir = await question(`本地上传目录 (默认: ./uploads): `) || './uploads';
    envVars.UPLOAD_DIR = uploadDir;
  }
  
  // 生产环境额外配置
  if (environment === 'production') {
    console.log('\n🌐 生产环境配置:');
    const corsOrigin = await question(`CORS 允许的域名: `);
    if (corsOrigin) {
      envVars.CORS_ORIGIN = corsOrigin;
    }
  }
  
  // 生成环境文件内容
  const envContent = Object.entries(envVars)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  const filename = `.env.${environment}`;
  writeFileSync(filename, envContent);
  
  console.log(`\n✅ ${filename} 文件已创建`);
  
  if (environment === 'production') {
    console.log('\n⚠️  重要提醒:');
    console.log('1. 请妥善保管生产环境的密钥和密码');
    console.log('2. 不要将包含真实密钥的文件提交到版本控制');
    console.log('3. 定期更换密码和密钥');
    console.log('4. 确保数据库和服务器的安全配置');
  }
}

/**
 * 检查环境配置一致性
 */
function checkConsistency() {
  console.log('\n🔍 检查环境配置一致性...\n');
  
  const environments = ['development', 'staging', 'production'];
  const configs = {};
  
  // 读取所有环境配置
  for (const env of environments) {
    const filename = `.env.${env}`;
    if (existsSync(filename)) {
      const content = readFileSync(filename, 'utf8');
      configs[env] = {};
      
      content.split('\n').forEach(line => {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          configs[env][key.trim()] = valueParts.join('=').trim();
        }
      });
    }
  }
  
  // 检查必需变量
  const requiredVars = [
    'NODE_ENV', 'PORT', 'VITE_API_BASE_URL', 'VITE_ENV',
    'DATABASE_URL', 'JWT_SECRET', 'ADMIN_USERNAME', 'ADMIN_PASSWORD'
  ];
  
  const issues = [];
  
  for (const env of environments) {
    if (!configs[env]) {
      issues.push(`❌ 缺少 .env.${env} 文件`);
      continue;
    }
    
    for (const varName of requiredVars) {
      if (!configs[env][varName]) {
        issues.push(`❌ ${env} 环境缺少变量: ${varName}`);
      }
    }
  }
  
  // 检查变量命名一致性
  const allVars = new Set();
  Object.values(configs).forEach(config => {
    Object.keys(config).forEach(key => allVars.add(key));
  });
  
  for (const varName of allVars) {
    const envs = environments.filter(env => configs[env] && configs[env][varName]);
    if (envs.length > 0 && envs.length < environments.length) {
      const missing = environments.filter(env => !configs[env] || !configs[env][varName]);
      issues.push(`⚠️  变量 ${varName} 在以下环境中缺失: ${missing.join(', ')}`);
    }
  }
  
  if (issues.length === 0) {
    console.log('✅ 环境配置一致性检查通过');
  } else {
    console.log('发现以下问题:');
    issues.forEach(issue => console.log(issue));
  }
  
  return issues.length === 0;
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 环境配置设置工具\n');
  
  const action = await question('选择操作:\n1. 创建环境配置\n2. 检查配置一致性\n3. 退出\n请选择 (1-3): ');
  
  switch (action) {
    case '1':
      const env = await question('\n选择环境:\n1. 开发环境 (development)\n2. 预发布环境 (staging)\n3. 生产环境 (production)\n请选择 (1-3): ');
      const envMap = { '1': 'development', '2': 'staging', '3': 'production' };
      if (envMap[env]) {
        await createEnvFile(envMap[env]);
      } else {
        console.log('❌ 无效选择');
      }
      break;
      
    case '2':
      checkConsistency();
      break;
      
    case '3':
      console.log('👋 再见!');
      break;
      
    default:
      console.log('❌ 无效选择');
  }
  
  rl.close();
}

// 运行主函数
main().catch(console.error);
