#!/usr/bin/env node

/**
 * 环境迁移助手
 * 帮助在不同部署环境之间迁移配置和数据
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { createInterface } from 'readline';
import { execSync } from 'child_process';

const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

/**
 * 迁移配置映射
 */
const MIGRATION_CONFIGS = {
  'vercel-to-aliyun': {
    name: 'Vercel 到阿里云',
    description: '从 Vercel + Neon 迁移到阿里云 ECS + RDS + OSS',
    envMappings: {
      // 数据库配置迁移
      'POSTGRES_PRISMA_URL': 'POSTGRES_PRISMA_URL',
      'POSTGRES_URL_NON_POOLING': 'POSTGRES_URL_NON_POOLING',
      'DATABASE_URL': 'DATABASE_URL',
      
      // 存储配置迁移
      'BLOB_READ_WRITE_TOKEN': null, // 移除 Vercel Blob
      'ALIYUN_ACCESS_KEY_ID': 'ALIYUN_ACCESS_KEY_ID',
      'ALIYUN_ACCESS_KEY_SECRET': 'ALIYUN_ACCESS_KEY_SECRET',
      'ALIYUN_OSS_REGION': 'ALIYUN_OSS_REGION',
      'ALIYUN_OSS_BUCKET': 'ALIYUN_OSS_BUCKET',
      'ALIYUN_OSS_ENDPOINT': 'ALIYUN_OSS_ENDPOINT',
      
      // 应用配置调整
      'NODE_ENV': 'production',
      'PORT': '3000',
      'VITE_API_BASE_URL': '/api',
      'VITE_SERVER_URL': '',
      'DEBUG': 'false',
      'LOG_LEVEL': 'warn'
    }
  },
  
  'aliyun-to-vercel': {
    name: '阿里云到 Vercel',
    description: '从阿里云 ECS + RDS + OSS 迁移到 Vercel + Neon + Blob',
    envMappings: {
      // 数据库配置迁移
      'POSTGRES_PRISMA_URL': 'POSTGRES_PRISMA_URL',
      'POSTGRES_URL_NON_POOLING': 'POSTGRES_URL_NON_POOLING',
      'DATABASE_URL': 'DATABASE_URL',
      
      // 存储配置迁移
      'ALIYUN_ACCESS_KEY_ID': null, // 移除阿里云 OSS
      'ALIYUN_ACCESS_KEY_SECRET': null,
      'ALIYUN_OSS_REGION': null,
      'ALIYUN_OSS_BUCKET': null,
      'ALIYUN_OSS_ENDPOINT': null,
      'BLOB_READ_WRITE_TOKEN': 'BLOB_READ_WRITE_TOKEN',
      
      // 应用配置调整
      'NODE_ENV': 'production',
      'PORT': '3000',
      'VITE_API_BASE_URL': '/api',
      'VITE_SERVER_URL': '',
      'DEBUG': 'false',
      'LOG_LEVEL': 'info'
    }
  },
  
  'local-to-production': {
    name: '本地到生产环境',
    description: '从本地开发环境迁移到生产环境',
    envMappings: {
      'NODE_ENV': 'production',
      'PORT': '3000',
      'VITE_ENV': 'production',
      'DEBUG': 'false',
      'LOG_LEVEL': 'warn',
      'ENABLE_API_DOCS': 'false'
    }
  }
};

/**
 * 读取环境配置文件
 */
function readEnvFile(filename) {
  if (!existsSync(filename)) {
    return {};
  }
  
  const content = readFileSync(filename, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      env[key.trim()] = valueParts.join('=').trim().replace(/^["']|["']$/g, '');
    }
  });
  
  return env;
}

/**
 * 写入环境配置文件
 */
function writeEnvFile(filename, env) {
  const content = Object.entries(env)
    .filter(([key, value]) => value !== null && value !== undefined)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  writeFileSync(filename, content);
}

/**
 * 生成迁移后的环境配置
 */
async function generateMigratedConfig(migrationType, sourceEnv) {
  const config = MIGRATION_CONFIGS[migrationType];
  const newEnv = { ...sourceEnv };
  
  console.log(`\n🔄 应用 ${config.name} 迁移配置...\n`);
  
  // 应用映射规则
  for (const [sourceKey, targetValue] of Object.entries(config.envMappings)) {
    if (targetValue === null) {
      // 移除变量
      delete newEnv[sourceKey];
      console.log(`❌ 移除变量: ${sourceKey}`);
    } else if (typeof targetValue === 'string' && !targetValue.includes('_')) {
      // 设置固定值
      newEnv[sourceKey] = targetValue;
      console.log(`✏️  设置变量: ${sourceKey} = ${targetValue}`);
    } else {
      // 保持变量但可能需要用户输入新值
      if (!newEnv[sourceKey]) {
        const newValue = await question(`请输入 ${sourceKey} 的新值: `);
        if (newValue) {
          newEnv[sourceKey] = newValue;
          console.log(`✅ 设置变量: ${sourceKey}`);
        }
      }
    }
  }
  
  return newEnv;
}

/**
 * 数据库迁移
 */
async function migrateDatabase(sourceDbUrl, targetDbUrl) {
  console.log('\n📊 开始数据库迁移...');
  
  try {
    // 导出源数据库
    console.log('📤 导出源数据库...');
    execSync(`pg_dump "${sourceDbUrl}" > migration_backup.sql`, { stdio: 'inherit' });
    
    // 导入到目标数据库
    console.log('📥 导入到目标数据库...');
    execSync(`psql "${targetDbUrl}" < migration_backup.sql`, { stdio: 'inherit' });
    
    console.log('✅ 数据库迁移完成');
    
    // 清理备份文件
    const keepBackup = await question('是否保留备份文件? (y/n): ');
    if (keepBackup.toLowerCase() !== 'y') {
      execSync('rm migration_backup.sql');
      console.log('🗑️  已清理备份文件');
    }
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error.message);
    console.log('💡 请手动执行数据库迁移或检查连接配置');
  }
}

/**
 * 文件存储迁移指导
 */
function showStorageMigrationGuide(migrationType) {
  console.log('\n📁 文件存储迁移指导:\n');
  
  if (migrationType === 'vercel-to-aliyun') {
    console.log('从 Vercel Blob 迁移到阿里云 OSS:');
    console.log('1. 下载 Vercel Blob 中的所有文件');
    console.log('2. 使用阿里云 OSS 控制台或 CLI 上传文件');
    console.log('3. 更新数据库中的文件 URL 引用');
    console.log('4. 测试文件访问功能');
  } else if (migrationType === 'aliyun-to-vercel') {
    console.log('从阿里云 OSS 迁移到 Vercel Blob:');
    console.log('1. 下载阿里云 OSS 中的所有文件');
    console.log('2. 使用 Vercel Blob API 上传文件');
    console.log('3. 更新数据库中的文件 URL 引用');
    console.log('4. 测试文件访问功能');
  }
  
  console.log('\n💡 建议使用专门的迁移脚本处理大量文件');
}

/**
 * 生成部署检查清单
 */
function generateDeploymentChecklist(migrationType) {
  console.log('\n📋 部署检查清单:\n');
  
  const commonChecks = [
    '✅ 环境变量配置正确',
    '✅ 数据库连接正常',
    '✅ 文件存储访问正常',
    '✅ API 接口功能测试',
    '✅ 前端页面加载正常',
    '✅ 用户认证功能正常',
    '✅ 文件上传功能正常',
    '✅ SSL 证书配置（生产环境）',
    '✅ 域名解析正确',
    '✅ 监控和日志配置'
  ];
  
  commonChecks.forEach(check => console.log(check));
  
  if (migrationType === 'vercel-to-aliyun') {
    console.log('\n阿里云特定检查:');
    console.log('✅ ECS 实例运行正常');
    console.log('✅ RDS 数据库配置正确');
    console.log('✅ OSS 存储桶权限配置');
    console.log('✅ 安全组规则配置');
    console.log('✅ 负载均衡配置（如需要）');
  }
}

/**
 * 主迁移流程
 */
async function runMigration() {
  console.log('🚀 环境迁移助手\n');
  
  // 选择迁移类型
  console.log('选择迁移类型:');
  Object.entries(MIGRATION_CONFIGS).forEach(([key, config], index) => {
    console.log(`${index + 1}. ${config.name} - ${config.description}`);
  });
  
  const choice = await question('\n请选择 (1-3): ');
  const migrationTypes = Object.keys(MIGRATION_CONFIGS);
  const migrationType = migrationTypes[parseInt(choice) - 1];
  
  if (!migrationType) {
    console.log('❌ 无效选择');
    return;
  }
  
  const config = MIGRATION_CONFIGS[migrationType];
  console.log(`\n📋 开始 ${config.name} 迁移\n`);
  
  // 读取源环境配置
  const sourceFile = await question('源环境配置文件路径 (默认: .env.local): ') || '.env.local';
  const sourceEnv = readEnvFile(sourceFile);
  
  if (Object.keys(sourceEnv).length === 0) {
    console.log(`❌ 无法读取源配置文件: ${sourceFile}`);
    return;
  }
  
  console.log(`✅ 已读取源配置 (${Object.keys(sourceEnv).length} 个变量)`);
  
  // 生成迁移后的配置
  const migratedEnv = await generateMigratedConfig(migrationType, sourceEnv);
  
  // 保存迁移后的配置
  const targetFile = await question('\n目标环境配置文件路径 (默认: .env.migrated): ') || '.env.migrated';
  writeEnvFile(targetFile, migratedEnv);
  console.log(`✅ 已保存迁移后的配置到: ${targetFile}`);
  
  // 数据库迁移
  const migrateDb = await question('\n是否需要迁移数据库? (y/n): ');
  if (migrateDb.toLowerCase() === 'y') {
    const sourceDbUrl = sourceEnv.DATABASE_URL;
    const targetDbUrl = migratedEnv.DATABASE_URL;
    
    if (sourceDbUrl && targetDbUrl && sourceDbUrl !== targetDbUrl) {
      await migrateDatabase(sourceDbUrl, targetDbUrl);
    } else {
      console.log('⚠️  数据库 URL 相同或缺失，跳过数据库迁移');
    }
  }
  
  // 显示文件存储迁移指导
  showStorageMigrationGuide(migrationType);
  
  // 生成部署检查清单
  generateDeploymentChecklist(migrationType);
  
  console.log('\n🎉 迁移助手完成！');
  console.log('📝 请按照检查清单验证迁移结果');
  console.log('📚 详细部署指南请参考: docs/DEPLOYMENT_GUIDE.md');
}

/**
 * 配置验证工具
 */
async function validateConfiguration() {
  console.log('🔍 配置验证工具\n');
  
  const envFile = await question('环境配置文件路径 (默认: .env.local): ') || '.env.local';
  const env = readEnvFile(envFile);
  
  if (Object.keys(env).length === 0) {
    console.log(`❌ 无法读取配置文件: ${envFile}`);
    return;
  }
  
  console.log(`✅ 已读取配置文件 (${Object.keys(env).length} 个变量)\n`);
  
  // 必需变量检查
  const requiredVars = [
    'NODE_ENV', 'PORT', 'DATABASE_URL', 'JWT_SECRET', 
    'ADMIN_USERNAME', 'ADMIN_PASSWORD'
  ];
  
  console.log('📋 必需变量检查:');
  requiredVars.forEach(varName => {
    if (env[varName]) {
      console.log(`✅ ${varName}`);
    } else {
      console.log(`❌ ${varName} - 缺失`);
    }
  });
  
  // 安全检查
  console.log('\n🔒 安全配置检查:');
  
  if (env.JWT_SECRET && env.JWT_SECRET.length >= 32) {
    console.log('✅ JWT_SECRET 长度充足');
  } else {
    console.log('❌ JWT_SECRET 长度不足或缺失');
  }
  
  if (env.ADMIN_PASSWORD && env.ADMIN_PASSWORD.length >= 8) {
    console.log('✅ ADMIN_PASSWORD 长度充足');
  } else {
    console.log('❌ ADMIN_PASSWORD 长度不足或缺失');
  }
  
  if (env.NODE_ENV === 'production' && env.DEBUG === 'true') {
    console.log('⚠️  生产环境建议关闭调试模式');
  }
  
  console.log('\n✅ 配置验证完成');
}

/**
 * 主函数
 */
async function main() {
  const action = await question('选择操作:\n1. 环境迁移\n2. 配置验证\n3. 退出\n请选择 (1-3): ');
  
  switch (action) {
    case '1':
      await runMigration();
      break;
    case '2':
      await validateConfiguration();
      break;
    case '3':
      console.log('👋 再见!');
      break;
    default:
      console.log('❌ 无效选择');
  }
  
  rl.close();
}

// 运行主函数
main().catch(console.error);
