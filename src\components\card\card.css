.card {
  display: flex;
  flex-direction: column;
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

/* 变体样式 */
.card--default {
  /* 默认样式已在基础类中定义 */
}

.card--elevated {
  box-shadow: var(--shadow-card);
}

.card--outlined {
  background: transparent;
  border: var(--spacing-xs) solid var(--color-border);
}

.card--glass {
  background: linear-gradient(144deg, rgba(0, 0, 0, 0.30) 0%, rgba(255, 255, 255, 0.05) 98.73%);
  border: 1px solid var(--card-border-default);
  box-shadow: var(--card-shadow-inset);
  backdrop-filter: blur(var(--spacing-md));
}

/* 内边距变体 */
.card--padding-none {
  padding: 0;
}

.card--padding-small {
  padding: var(--spacing-sm);
}

.card--padding-medium {
  padding: var(--spacing-md);
}

.card--padding-large {
  padding: var(--spacing-lg);
}

/* 圆角变体 */
.card--radius-none {
  border-radius: 0;
}

.card--radius-small {
  border-radius: var(--radius-sm);
}

.card--radius-medium {
  border-radius: var(--radius-base);
}

.card--radius-large {
  border-radius: var(--radius-lg);
}

/* 交互状态 */
.card--clickable {
  cursor: pointer;
}

.card--clickable:focus {
  outline: var(--spacing-xs) solid var(--color-brand);
  outline-offset: var(--spacing-xs);
}

.card--hoverable:hover {
  border-color: var(--color-border-hover);
  transform: translateY(calc(-1 * var(--spacing-xs)));
}

.card--glass.card--hoverable:hover {
  border-color: var(--color-border-hover);
  box-shadow: var(--shadow-card-hover);
}

.card--elevated.card--hoverable:hover {
  box-shadow: var(--shadow-card-hover);
}

/* 组合状态 */
.card--clickable.card--hoverable:hover {
  border-color: var(--color-border-hover);
  transform: translateY(calc(-1 * var(--spacing-xs)));
}

.card--clickable.card--hoverable:active {
  transform: translateY(0);
}
