/* 材质预览组件样式 */
.material-preview {
  border-radius: 50%;
  overflow: hidden;
  background: var(--color-bg-overlay);
  position: relative;
}

.material-preview canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

/* 加载状态 - 使用统一的Loading组件，移除重复的动画定义 */
.material-preview--loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-overlay);
}
