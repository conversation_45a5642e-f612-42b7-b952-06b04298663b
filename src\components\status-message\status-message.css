.status-message {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-base);
  border-radius: var(--radius-base);
  background: var(--color-bg-overlay);
  border: 1px solid var(--color-border);
}

.status-message__icon {
  flex-shrink: 0;
  margin-top: 2px; /* 微调对齐 */
}

.status-message__content {
  flex: 1;
  min-width: 0;
}

.status-message__message {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-base);
  margin: 0;
}

.status-message__description {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-base);
  margin: var(--spacing-xs) 0 0 0;
  opacity: var(--opacity-secondary);
}

/* 类型变体 */
.status-message--info {
  border-color: var(--color-info);
  background: var(--color-info-bg);
}

.status-message--info .status-message__icon {
  color: var(--color-info);
}

.status-message--info .status-message__message {
  color: var(--color-info-text);
}

.status-message--success {
  border-color: var(--color-success);
  background: var(--color-success-bg);
}

.status-message--success .status-message__icon {
  color: var(--color-success);
}

.status-message--success .status-message__message {
  color: var(--color-success-text);
}

.status-message--warning {
  border-color: var(--color-warning);
  background: var(--color-warning-bg);
}

.status-message--warning .status-message__icon {
  color: var(--color-warning);
}

.status-message--warning .status-message__message {
  color: var(--color-warning-text);
}

.status-message--error {
  border-color: var(--color-error);
  background: var(--color-error-bg);
}

.status-message--error .status-message__icon {
  color: var(--color-error);
}

.status-message--error .status-message__message {
  color: var(--color-error-text);
}

/* 尺寸变体 */
.status-message--small {
  padding: var(--spacing-xs) var(--spacing-sm);
  gap: var(--spacing-xs);
}

.status-message--small .status-message__icon {
  width: var(--icon-size-small);
  height: var(--icon-size-small);
}

.status-message--small .status-message__message {
  font-size: var(--font-size-sm);
}

.status-message--small .status-message__description {
  font-size: var(--font-size-xs);
}

.status-message--medium .status-message__icon {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}

.status-message--large {
  padding: var(--spacing-md) var(--spacing-base);
  gap: var(--spacing-base);
}

.status-message--large .status-message__icon {
  width: var(--icon-size-large);
  height: var(--icon-size-large);
}

.status-message--large .status-message__message {
  font-size: var(--font-size-lg);
}

.status-message--large .status-message__description {
  font-size: var(--font-size-base);
}
