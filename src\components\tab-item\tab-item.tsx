import React from 'react';
import type { LucideIcon } from 'lucide-react';
import './tab-item.css';

export interface TabItemProps {
  /** 标签文本 */
  label: string;
  /** 图标组件 */
  icon?: LucideIcon;
  /** 是否选中 */
  isActive?: boolean;
  /** 点击事件处理函数 */
  onClick?: () => void;
  /** 自定义类名 */
  className?: string;
  /** @deprecated 使用 widthVariant 替代 */
  width?: number | string;
  /** 宽度变体 */
  widthVariant?: 'compact' | 'default' | 'wide';
}

export const TabItem: React.FC<TabItemProps> = ({
  label,
  icon: Icon,
  isActive = false,
  onClick,
  className = '',
  width = 104,
  widthVariant = 'default'
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  // 确定宽度类名
  const getWidthClass = () => {
    if (width !== 104) return ''; // 保持向后兼容，使用内联样式
    if (widthVariant === 'default') return 'tab-item--width-default';
    return `tab-item--${widthVariant}`;
  };

  const containerClasses = [
    'tab-item',
    isActive ? 'tab-item--active' : 'tab-item--default',
    getWidthClass(),
    className
  ].filter(Boolean).join(' ');

  // 选中和非选中状态使用不同的文本颜色
  const textColorClass = isActive ? 'tab-item__text--active' : 'tab-item__text--default';
  const iconColorClass = isActive ? 'tab-item__icon--active' : 'tab-item__icon--default';

  return (
    <div
      className={containerClasses}
      onClick={handleClick}
      style={width !== 104 ? { width: typeof width === 'number' ? `${width}px` : width } : undefined}
      data-layer="tab-item"
    >
      {Icon && (
        <div className={`tab-item__icon-container ${iconColorClass}`}>
          <Icon size={16} />
        </div>
      )}
      <div className={`tab-item__text ${textColorClass}`}>{label}</div>
    </div>
  );
};
