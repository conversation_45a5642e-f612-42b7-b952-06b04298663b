import { type FC, type ReactNode, useRef, useState, useLayoutEffect } from 'react';
import { createPortal } from 'react-dom';
import './tooltip.css';

interface TooltipProps {
  children: ReactNode;
  content: ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}

export const Tooltip: FC<TooltipProps> = ({ children, content, position = 'top', className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (isVisible && tooltipRef.current && containerRef.current) {
      const tooltipEl = tooltipRef.current;
      const containerRect = containerRef.current.getBoundingClientRect();
      const tooltipRect = tooltipEl.getBoundingClientRect();

      let top = 0;
      let left = 0;

      switch (position) {
        case 'top':
          top = containerRect.top - tooltipRect.height - 8;
          left = containerRect.left + (containerRect.width - tooltipRect.width) / 2;
          break;
        case 'bottom':
          top = containerRect.bottom + 8;
          left = containerRect.left + (containerRect.width - tooltipRect.width) / 2;
          break;
        case 'left':
          top = containerRect.top + (containerRect.height - tooltipRect.height) / 2;
          left = containerRect.left - tooltipRect.width - 8;
          break;
        case 'right':
          top = containerRect.top + (containerRect.height - tooltipRect.height) / 2;
          left = containerRect.right + 8;
          break;
      }

      tooltipEl.style.top = `${top + window.scrollY}px`;
      tooltipEl.style.left = `${left + window.scrollX}px`;

      // 从触发元素向上查找主题class，并同步到tooltip上
      const themeElement = containerRef.current?.closest('[class*="theme-"]');
      if (themeElement) {
        const themeClass = Array.from(themeElement.classList).find(c => c.startsWith('theme-'));
        if (themeClass) {
          tooltipEl.classList.add(themeClass);
        }
      }
    }
  }, [isVisible, position]);

  const tooltipContent = isVisible ? createPortal(
    <div
      className={`tooltip tooltip--${position} ${className}`}
      ref={tooltipRef}
      style={{ position: 'absolute' }}
    >
      {content}
    </div>,
    document.body
  ) : null;

  return (
    <>
      <div
        className="tooltip-container"
        ref={containerRef}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
      >
        {children}
      </div>
      {tooltipContent}
    </>
  );
};