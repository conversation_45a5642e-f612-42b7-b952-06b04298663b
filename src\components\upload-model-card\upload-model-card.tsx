import React, { useRef } from 'react';
import { Upload } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { handleFileInputChange, FILE_VALIDATION_CONFIGS } from '../../utils/fileUpload';
import './upload-model-card.css';

interface UploadModelCardProps {
  className?: string;
}

export const UploadModelCard: React.FC<UploadModelCardProps> = ({ className = '' }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const handleCardClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (file: File) => {
    // 将文件传递到渲染页面（使用路由 state）
    navigate('/render', {
      state: { uploadedFile: file }
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileInputChange(e, handleFileSelect, FILE_VALIDATION_CONFIGS.MODEL, (error) => alert(error));
  };

  return (
    <div className={`model-card upload-model-card ${className}`} onClick={handleCardClick}>
      <div className="upload-model-card__icon">
        <Upload size={36} />
      </div>
      <h3 className="model-card__title">上传模型</h3>
      {/* 隐藏的文件选择 */}
      <input
        type="file"
        ref={fileInputRef}
        accept=".glb,.gltf"
className="file-input"
        onChange={handleFileChange}
      />
    </div>
  );
};
