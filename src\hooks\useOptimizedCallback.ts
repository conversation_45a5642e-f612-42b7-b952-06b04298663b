import { useCallback, useRef, useMemo } from 'react';

/**
 * 优化的回调Hook，提供更智能的依赖管理
 */
export const useOptimizedCallback = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  deps: React.DependencyList
): T => {
  const callbackRef = useRef(callback);
  const depsRef = useRef(deps);

  // 深度比较依赖项
  const depsChanged = useMemo(() => {
    if (depsRef.current.length !== deps.length) {
      return true;
    }
    
    return deps.some((dep, index) => {
      const prevDep = depsRef.current[index];
      
      // 对象深度比较
      if (typeof dep === 'object' && dep !== null && typeof prevDep === 'object' && prevDep !== null) {
        return JSON.stringify(dep) !== JSON.stringify(prevDep);
      }
      
      return dep !== prevDep;
    });
  }, deps);

  // 只有在依赖真正改变时才更新回调
  if (depsChanged) {
    callbackRef.current = callback;
    depsRef.current = deps;
  }

  return useCallback(callbackRef.current, [depsChanged]) as T;
};

/**
 * 防抖Hook
 */
export const useDebounce = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback(
    ((...args: unknown[]) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }) as T,
    [callback, delay, ...deps]
  );
};

/**
 * 节流Hook
 */
export const useThrottle = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T => {
  const lastCallRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback(
    ((...args: unknown[]) => {
      const now = Date.now();
      
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now;
        callback(...args);
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCallRef.current = Date.now();
          callback(...args);
        }, delay - (now - lastCallRef.current));
      }
    }) as T,
    [callback, delay, ...deps]
  );
};

/**
 * 批处理Hook - 将多个调用批处理为单个调用
 */
export const useBatchCallback = <T>(
  callback: (items: T[]) => void,
  batchSize: number = 10,
  delay: number = 100
) => {
  const batchRef = useRef<T[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const flush = useCallback(() => {
    if (batchRef.current.length > 0) {
      callback([...batchRef.current]);
      batchRef.current = [];
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
  }, [callback]);

  const addToBatch = useCallback((item: T) => {
    batchRef.current.push(item);
    
    // 如果达到批处理大小，立即执行
    if (batchRef.current.length >= batchSize) {
      flush();
      return;
    }
    
    // 否则设置延迟执行
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(flush, delay);
  }, [batchSize, delay, flush]);

  return { addToBatch, flush };
};

/**
 * 缓存Hook - 基于参数缓存函数结果
 */
export const useMemoizedCallback = <TArgs extends unknown[], TReturn>(
  callback: (...args: TArgs) => TReturn,
  maxCacheSize: number = 10
) => {
  const cacheRef = useRef<Map<string, { result: TReturn; timestamp: number }>>(new Map());

  return useCallback(
    (...args: TArgs): TReturn => {
      const key = JSON.stringify(args);
      const cached = cacheRef.current.get(key);
      
      if (cached) {
        return cached.result;
      }
      
      const result = callback(...args);
      
      // 如果缓存已满，删除最旧的条目
      if (cacheRef.current.size >= maxCacheSize) {
        let oldestKey = '';
        let oldestTime = Infinity;
        
        for (const [k, v] of cacheRef.current.entries()) {
          if (v.timestamp < oldestTime) {
            oldestTime = v.timestamp;
            oldestKey = k;
          }
        }
        
        if (oldestKey) {
          cacheRef.current.delete(oldestKey);
        }
      }
      
      cacheRef.current.set(key, { result, timestamp: Date.now() });
      return result;
    },
    [callback, maxCacheSize]
  );
};

/**
 * 异步回调Hook - 处理异步操作的取消
 */
export const useAsyncCallback = <TArgs extends unknown[], TReturn>(
  callback: (...args: TArgs) => Promise<TReturn>
) => {
  const abortControllerRef = useRef<AbortController>();

  const execute = useCallback(
    async (...args: TArgs): Promise<TReturn | null> => {
      // 取消之前的操作
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // 创建新的控制器
      abortControllerRef.current = new AbortController();
      
      try {
        const result = await callback(...args);
        
        // 检查是否被取消
        if (abortControllerRef.current.signal.aborted) {
          return null;
        }
        
        return result;
      } catch (error) {
        // 如果是取消错误，返回null
        if (error instanceof Error && error.name === 'AbortError') {
          return null;
        }
        throw error;
      }
    },
    [callback]
  );

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return { execute, cancel };
};

/**
 * 条件回调Hook - 只在满足条件时执行回调
 */
export const useConditionalCallback = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  condition: boolean | (() => boolean),
  fallback?: T
): T => {
  return useCallback(
    ((...args: unknown[]) => {
      const shouldExecute = typeof condition === 'function' ? condition() : condition;
      
      if (shouldExecute) {
        return callback(...args);
      } else if (fallback) {
        return fallback(...args);
      }
      
      return undefined;
    }) as T,
    [callback, condition, fallback]
  );
};
