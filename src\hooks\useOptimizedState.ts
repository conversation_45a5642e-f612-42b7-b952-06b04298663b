import { useState, useCallback, useRef } from 'react';

/**
 * 优化的状态Hook，提供更好的性能和功能
 */
export const useOptimizedState = <T>(
  initialState: T | (() => T),
  options: {
    /** 是否启用历史记录 */
    enableHistory?: boolean;
    /** 历史记录最大长度 */
    maxHistoryLength?: number;
    /** 状态比较函数 */
    compare?: (prev: T, next: T) => boolean;
    /** 状态变化回调 */
    onChange?: (newState: T, prevState: T) => void;
  } = {}
) => {
  const {
    enableHistory = false,
    maxHistoryLength = 10,
    compare = (prev, next) => prev === next,
    onChange
  } = options;

  const [state, setStateInternal] = useState(initialState);
  const historyRef = useRef<T[]>([]);
  const prevStateRef = useRef<T>(state);

  // 优化的setState函数
  const setState = useCallback((newState: T | ((prev: T) => T)) => {
    setStateInternal(prevState => {
      const nextState = typeof newState === 'function' 
        ? (newState as (prev: T) => T)(prevState)
        : newState;

      // 如果状态没有变化，不更新
      if (compare(prevState, nextState)) {
        return prevState;
      }

      // 添加到历史记录
      if (enableHistory) {
        historyRef.current.push(prevState);
        if (historyRef.current.length > maxHistoryLength) {
          historyRef.current.shift();
        }
      }

      // 调用变化回调
      onChange?.(nextState, prevState);
      prevStateRef.current = prevState;

      return nextState;
    });
  }, [compare, enableHistory, maxHistoryLength, onChange]);

  // 撤销功能
  const undo = useCallback(() => {
    if (!enableHistory || historyRef.current.length === 0) {
      return false;
    }

    const previousState = historyRef.current.pop()!;
    setStateInternal(previousState);
    return true;
  }, [enableHistory]);

  // 重置状态
  const reset = useCallback(() => {
    const initial = typeof initialState === 'function' 
      ? (initialState as () => T)()
      : initialState;
    
    setState(initial);
    historyRef.current = [];
  }, [initialState, setState]);

  // 批量更新
  const batchUpdate = useCallback((updates: Partial<T>[]) => {
    setState(prevState => {
      let newState = { ...prevState };
      
      for (const update of updates) {
        newState = { ...newState, ...update };
      }
      
      return newState;
    });
  }, [setState]);

  return {
    state,
    setState,
    undo,
    reset,
    batchUpdate,
    history: historyRef.current,
    canUndo: enableHistory && historyRef.current.length > 0,
    prevState: prevStateRef.current
  };
};

/**
 * 深度状态Hook - 处理嵌套对象状态
 */
export const useDeepState = <T extends Record<string, unknown>>(
  initialState: T
) => {
  const [state, setState] = useState<T>(initialState);

  // 深度更新函数
  const updateDeep = useCallback((path: string, value: unknown) => {
    setState(prevState => {
      const newState = { ...prevState };
      const keys = path.split('.');
      let current: Record<string, unknown> = newState;

      // 导航到目标位置
      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!(key in current) || typeof current[key] !== 'object') {
          current[key] = {};
        } else {
          current[key] = { ...current[key] };
        }
        current = current[key];
      }

      // 设置值
      current[keys[keys.length - 1]] = value;
      return newState;
    });
  }, []);

  // 获取深度值
  const getDeep = useCallback((path: string) => {
    const keys = path.split('.');
    let current: unknown = state;

    for (const key of keys) {
      if (current && typeof current === 'object' && key in (current as Record<string, unknown>)) {
        current = (current as Record<string, unknown>)[key];
      } else {
        return undefined;
      }
    }

    return current;
  }, [state]);

  // 删除深度属性
  const deleteDeep = useCallback((path: string) => {
    setState(prevState => {
      const newState = { ...prevState };
      const keys = path.split('.');
      let current: Record<string, unknown> = newState;

      // 导航到父级
      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!(key in current) || typeof current[key] !== 'object') {
          return prevState; // 路径不存在
        }
        current[key] = { ...current[key] };
        current = current[key];
      }

      // 删除属性
      delete current[keys[keys.length - 1]];
      return newState;
    });
  }, []);

  return {
    state,
    setState,
    updateDeep,
    getDeep,
    deleteDeep
  };
};

/**
 * 列表状态Hook - 优化列表操作
 */
export const useListState = <T>(
  initialList: T[] = [],
  keyExtractor?: (item: T, index: number) => string | number
) => {
  const [list, setList] = useState<T[]>(initialList);

  // 添加项目
  const addItem = useCallback((item: T, index?: number) => {
    setList(prevList => {
      if (index === undefined) {
        return [...prevList, item];
      }
      
      const newList = [...prevList];
      newList.splice(index, 0, item);
      return newList;
    });
  }, []);

  // 删除项目
  const removeItem = useCallback((index: number) => {
    setList(prevList => prevList.filter((_, i) => i !== index));
  }, []);

  // 根据键删除项目
  const removeItemByKey = useCallback((key: string | number) => {
    if (!keyExtractor) return;
    
    setList(prevList => 
      prevList.filter((item, index) => keyExtractor(item, index) !== key)
    );
  }, [keyExtractor]);

  // 更新项目
  const updateItem = useCallback((index: number, updater: T | ((prev: T) => T)) => {
    setList(prevList => 
      prevList.map((item, i) => {
        if (i === index) {
          return typeof updater === 'function' 
            ? (updater as (prev: T) => T)(item)
            : updater;
        }
        return item;
      })
    );
  }, []);

  // 移动项目
  const moveItem = useCallback((fromIndex: number, toIndex: number) => {
    setList(prevList => {
      const newList = [...prevList];
      const [movedItem] = newList.splice(fromIndex, 1);
      newList.splice(toIndex, 0, movedItem);
      return newList;
    });
  }, []);

  // 清空列表
  const clearList = useCallback(() => {
    setList([]);
  }, []);

  // 批量操作
  const batchUpdate = useCallback((operations: Array<{
    type: 'add' | 'remove' | 'update' | 'move';
    payload: Record<string, unknown>;
  }>) => {
    setList(prevList => {
      let newList = [...prevList];
      
      for (const operation of operations) {
        switch (operation.type) {
          case 'add': {
            const { item, index } = operation.payload;
            if (index === undefined) {
              newList.push(item);
            } else {
              newList.splice(index, 0, item);
            }
            break;
          }

          case 'remove':
            newList = newList.filter((_, i) => i !== operation.payload.index);
            break;

          case 'update': {
            const { index: updateIndex, updater } = operation.payload;
            newList[updateIndex] = typeof updater === 'function'
              ? updater(newList[updateIndex])
              : updater;
            break;
          }

          case 'move': {
            const { fromIndex, toIndex } = operation.payload;
            const [movedItem] = newList.splice(fromIndex, 1);
            newList.splice(toIndex, 0, movedItem);
            break;
          }
        }
      }
      
      return newList;
    });
  }, []);

  // 计算派生状态
  const isEmpty = list.length === 0;
  const length = list.length;
  const first = list[0];
  const last = list[list.length - 1];

  return {
    list,
    setList,
    addItem,
    removeItem,
    removeItemByKey,
    updateItem,
    moveItem,
    clearList,
    batchUpdate,
    isEmpty,
    length,
    first,
    last
  };
};
