/* Shared styles for admin management tables */

.management-toolbar{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.admin-table-container {
  overflow-x: auto;
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-base);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-base);
  color: var(--color-content-regular);
}

.admin-table tbody tr {
  height: 80px;
}

.admin-table td {
  height: 100%;
}

.admin-table th,
.admin-table td {
  padding: var(--spacing-base) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  vertical-align: middle;
}

.admin-table th {
  font-weight: 500;
  white-space: nowrap;
}

.admin-table tbody tr:hover {
  background-color: var(--color-bg-hover);
}

/* Common column styles */
.admin-table .thumbnail-cell {
  width: 80px;
  min-width: 60px;
  max-width: 100px;
}

.admin-table .thumbnail-image {
  max-width: 80px;
  max-height: 60px;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
}

.admin-table .thumbnail-placeholder {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-overlay);
  border-radius: var(--radius-sm);
}

.admin-table .date-cell {
  white-space: nowrap;
  min-width: 100px;
}

.admin-table .actions-cell {
  width: 120px;
  text-align: left;
}

.admin-table .actions-cell > .icon-button + .icon-button {
  margin-left: var(--spacing-sm);
}

/* Modal Form Styles */
.modal-form .form-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  margin-bottom: 16px;
}

.modal-form .form-row-flex {
  display: flex;
  gap: 20px;
  align-items: flex-end; /* Align to bottom for better look with labels */
  margin-bottom: 20px;
}

.material-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  width: 100%;
}

.preview-sphere {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-base);
  background-color: var(--color-bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.material-settings {
  margin-top: 20px;
}

.model-upload-group {
  flex: 1;
}

.thumbnail-upload-group {
  display: flex;
  flex-direction: column;
}

.thumbnail-uploader {
  width: var(--spacing-giant);
  height: var(--spacing-giant);
  border: var(--spacing-xs) dashed var(--color-border);
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.2s;
  background-color: var(--color-bg-overlay);
  overflow: hidden;
}

.thumbnail-uploader:hover,
.thumbnail-uploader.drag-over {
  border-color: var(--color-brand);
}

.thumbnail-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder-icon {
  color: var(--color-content-secondary);
}

/* 当前缩略图样式 */
.current-thumbnail {
  width: var(--spacing-xxl);
  vertical-align: middle;
  margin-left: var(--spacing-sm);
}

/* 颜色预览样式 */
.color-preview {
  width: var(--spacing-xl);
  height: var(--spacing-xl);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-border);
  background-color: var(--preview-color, var(--color-content-mute));
}

/* 进度条样式 */
.progress-bar-container {
  width: 100%;
  height: var(--spacing-xs);
  background-color: var(--color-bg-overlay);
  border-radius: var(--radius-xs);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-brand);
  width: var(--progress-width, 0%);
  border-radius: var(--radius-xs);
  transition: width 0.3s ease;
}