.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--color-bg-page);
}

.login-card {
  width: 400px;
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-m);
  padding: var(--spacing-xxl);
  box-shadow: var(--shadow-modal);
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-xxl);
}

.login-logo {
  height: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
}

.login-header h2 {
  color: var(--color-content-accent);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.input-group label {
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
}

.login-error {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  text-align: center;
}
