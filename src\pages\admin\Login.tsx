import { useState } from 'react';
import { InputBox } from '../../components/input-box/input-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import './Login.css';
import { User, Lock } from 'lucide-react';
import LogoPng from '../../assets/images/Logo.png';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!username || !password) {
      setError('请输入用户名和密码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 保存认证信息
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('sessionId', data.sessionId);
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('user', JSON.stringify(data.user));

        // 跳转到管理面板
        window.location.href = '/admin/dashboard';
      } else {
        setError(data.error || '登录失败，请检查用户名和密码');
      }
    } catch (error) {
      console.error('登录错误:', error);
      setError('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container theme-dark">
      <div className="login-card">
        <div className="login-header">
          <img className="login-logo" src={LogoPng} alt="RINKO" />
          <h2>会通智能色彩云库 - 后台管理</h2>
        </div>
        
        <div className="login-form">
          <div className="input-group">
            <label>用户名</label>
            <InputBox 
              placeholder="请输入用户名" 
              value={username} 
              onChange={setUsername} 
              prefixIcon={<User size={18} />}
              fullWidth
            />
          </div>
          
          <div className="input-group">
            <label>密码</label>
            <InputBox 
              placeholder="请输入密码" 
              value={password} 
              onChange={setPassword}
              prefixIcon={<Lock size={18} />}
              type="password"
              fullWidth
            />
          </div>
          
          {error && <div className="login-error">{error}</div>}

          <PrimaryButton
            onClick={handleLogin}
            fullWidth
            showIcon={false}
            disabled={loading}
          >
            {loading ? '登录中...' : '登录'}
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};

export default Login;
