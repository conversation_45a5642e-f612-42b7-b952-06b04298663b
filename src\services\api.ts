import { upload, type UploadOptions } from '@vercel/blob/client';

export interface RawModelData {
  id: string;
  name: string;
  thumbnail: string | null;
  fileType: string | null;
  size: string | null;
  createdAt: string;
  filePath: string; // This is the pathname in blob storage
  url: string;      // This is the full URL to the blob
}

export interface ModelData {
  id: string;
  name: string;
  thumbnail: string | null;
  fileType: string | null;
  size: number | null;
  createdAt: string;
  filePath: string; // This will be the full URL
}

export interface MaterialData {
  id: string;
  name: string;
  thumbnailPath: string | null;
  color: string;
  metalness: number;
  roughness: number;
  glass: number;
  createdAt: string;
}

// Use relative path for production, absolute for development
const BASE_URL = '/api';

// Unified error handling for API requests
const handleApiResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: `HTTP error! status: ${response.status}` }));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }
  // For 204 No Content, response.json() will fail, so we return a placeholder.
  if (response.status === 204) {
    return {} as T;
  }
  return response.json();
};

// Transform model data from API to frontend format
const transformModelData = (model: RawModelData): ModelData => {
  const sizeInBytes = model.size ? parseInt(model.size, 10) : null;
  return {
    id: model.id,
    name: model.name,
    thumbnail: model.thumbnail,
    fileType: model.fileType,
    size: sizeInBytes && !isNaN(sizeInBytes) ? sizeInBytes : null,
    createdAt: model.createdAt,
    filePath: model.url, // Use the full URL from blob
  };
};

// Transform material data from API to frontend format
const transformMaterialData = (material: MaterialData): MaterialData => {
    return {
        ...material,
        thumbnailPath: material.thumbnailPath || '',
    };
}

class ApiService {
  // --- Uploader ---
  async uploadFile(file: File, onUploadProgress?: (progress: number) => void): Promise<string> {
    try {
      const handleUploadUrl = `${BASE_URL}/upload`;
      
      const newBlob = await upload(file.name, file, {
        access: 'public',
        handleUploadUrl,
        // The onUploadProgress callback is correctly implemented, but the library's
        // type definitions might be out of sync. We cast to a compatible type
        // to satisfy TypeScript without resorting to `any`.
        onUploadProgress: ({ progress }: { progress: number }) => onUploadProgress?.(progress),
      } as UploadOptions & { onUploadProgress: ({ progress }: { progress: number }) => void });
      return newBlob.url;
    } catch (error) {
      console.error('Upload failed:', error);
      throw new Error('文件上传失败，请检查网络连接或联系管理员。');
    }
  }

  // --- Model Methods ---
  async getModels(): Promise<ModelData[]> {
    try {
      const response = await fetch(`${BASE_URL}/models`);
      const models = await handleApiResponse<RawModelData[]>(response);
      return models.map(transformModelData);
    } catch (error) {
      console.error('Failed to fetch models:', error);
      return [];
    }
  }

  async getModel(id: string): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`);
      const model = await handleApiResponse<RawModelData>(response);
      return transformModelData(model);
    } catch (error) {
      console.error(`Failed to get model ${id}:`, error);
      return null;
    }
  }

  async addModel(modelData: {
    name: string;
    fileType: string;
    size: string;
    url: string;
    thumbnailUrl?: string | null;
  }): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(modelData),
      });
      const newModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(newModel);
    } catch (error) {
      console.error('Failed to add model:', error);
      return null;
    }
  }
  
  async updateModel(id: string, name: string, thumbnailUrl?: string | null): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, thumbnailUrl }),
      });
      const updatedModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(updatedModel);
    } catch (error) {
      console.error(`Failed to update model ${id}:`, error);
      return null;
    }
  }

  async deleteModel(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, { method: 'DELETE' });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete model ${id}:`, error);
      return false;
    }
  }

  // --- Material Methods ---
  async getMaterials(): Promise<MaterialData[]> {
    try {
      const response = await fetch(`${BASE_URL}/materials`);
      const materials = await handleApiResponse<MaterialData[]>(response);
      return materials.map(transformMaterialData);
    } catch (error) {
      console.error('Failed to fetch materials:', error);
      return [];
    }
  }

  async createMaterial(data: Partial<Omit<MaterialData, 'id' | 'createdAt'>>): Promise<MaterialData> {
    try {
      const response = await fetch(`${BASE_URL}/materials`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      const newMaterial = await handleApiResponse<MaterialData>(response);
      return transformMaterialData(newMaterial);
    } catch (error) {
      console.error('Failed to create material:', error);
      throw error;
    }
  }

  async updateMaterial(id: string, data: Partial<Omit<MaterialData, 'id' | 'createdAt'>>): Promise<MaterialData> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      const updatedMaterial = await handleApiResponse<MaterialData>(response);
      return transformMaterialData(updatedMaterial);
    } catch (error) {
      console.error(`Failed to update material ${id}:`, error);
      throw error;
    }
  }

  async deleteMaterial(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, { method: 'DELETE' });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete material ${id}:`, error);
      return false;
    }
  }
}

// Export a single instance of the service
export const apiService = new ApiService();