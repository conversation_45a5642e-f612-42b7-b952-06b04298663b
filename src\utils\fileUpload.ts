/**
 * 文件上传相关的工具函数
 */

export interface FileValidationOptions {
  /** 允许的文件扩展名 */
  allowedExtensions: string[];
  /** 最大文件大小（字节） */
  maxSize?: number;
  /** 文件类型描述（用于错误提示） */
  typeDescription: string;
}

export interface DragUploadHandlers {
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
}

/**
 * 验证文件是否符合要求
 */
export const validateFile = (
  file: File,
  options: FileValidationOptions
): { isValid: boolean; error?: string } => {
  const { allowedExtensions, maxSize, typeDescription } = options;
  
  // 检查文件扩展名
  const ext = file.name.split('.').pop()?.toLowerCase() || '';
  if (!allowedExtensions.includes(ext)) {
    return {
      isValid: false,
      error: `只支持 ${typeDescription} 格式文件`
    };
  }
  
  // 检查文件大小
  if (maxSize && file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    return {
      isValid: false,
      error: `文件大小不能超过${maxSizeMB}MB`
    };
  }
  
  return { isValid: true };
};

/**
 * 创建拖拽上传处理器
 */
export const createDragUploadHandlers = (
  onFileSelect: (file: File) => void,
  setDragOver: (dragOver: boolean) => void,
  validationOptions: FileValidationOptions,
  onError?: (error: string) => void
): DragUploadHandlers => {
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
    
    const file = e.dataTransfer.files?.[0];
    if (!file) return;
    
    const validation = validateFile(file, validationOptions);
    if (!validation.isValid) {
      if (onError) {
        onError(validation.error!);
      } else {
        alert(validation.error);
      }
      return;
    }
    
    onFileSelect(file);
  };

  return {
    onDragOver: handleDragOver,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop
  };
};

/**
 * 处理文件输入变化
 */
export const handleFileInputChange = (
  e: React.ChangeEvent<HTMLInputElement>,
  onFileSelect: (file: File) => void,
  validationOptions: FileValidationOptions,
  onError?: (error: string) => void
) => {
  const file = e.target.files?.[0];
  if (!file) return;
  
  const validation = validateFile(file, validationOptions);
  if (!validation.isValid) {
    if (onError) {
      onError(validation.error!);
    } else {
      alert(validation.error);
    }
    return;
  }
  
  onFileSelect(file);
};

/**
 * 常用的文件验证配置
 */
export const FILE_VALIDATION_CONFIGS = {
  MODEL: {
    allowedExtensions: ['glb', 'gltf'],
    maxSize: 50 * 1024 * 1024, // 50MB
    typeDescription: 'GLB / GLTF'
  },
  IMAGE: {
    allowedExtensions: ['png', 'jpg', 'jpeg', 'webp'],
    maxSize: 10 * 1024 * 1024, // 10MB
    typeDescription: 'PNG / JPG / WEBP'
  },
  GLB_ONLY: {
    allowedExtensions: ['glb'],
    typeDescription: 'GLB'
  }
} as const;
